# 课程排课系统 - UniApp H5 前端

这是一个基于 UniApp 开发的课程排课系统 H5 前端，与后端 Go API 进行交互，实现完整的课程排课管理功能。

## 功能特性

### 核心功能
- **教师管理**: 添加教师信息，设置授课科目
- **学生管理**: 添加学生信息
- **学生组管理**: 创建学生组，分配学生
- **排课任务**: 创建和管理排课任务
- **时间偏好设置**: 为教师和学生设置时间偏好
- **课程需求配置**: 配置学生组与教师的课程需求
- **排课执行**: 执行智能排课算法
- **结果查看**: 查看排课结果和冲突分析

### 界面特性
- 响应式设计，适配 H5 端
- 现代化 UI 设计
- 底部 Tab 导航
- 模态弹窗交互
- 实时数据更新

## 技术栈

- **框架**: UniApp (Vue 3 + TypeScript)
- **UI**: 原生 CSS + UniApp 组件
- **状态管理**: Vue 3 Composition API
- **HTTP 客户端**: UniApp 原生 request API
- **构建工具**: Vite

## 项目结构

```
uniapp/
├── src/
│   ├── pages/                 # 页面文件
│   │   ├── index/            # 首页
│   │   ├── teachers/         # 教师管理
│   │   ├── students/         # 学生管理
│   │   ├── groups/           # 学生组管理
│   │   ├── tasks/            # 排课任务
│   │   ├── preferences/      # 时间偏好设置
│   │   └── schedules/        # 排课结果
│   ├── utils/
│   │   └── api.ts           # API 接口封装
│   ├── static/              # 静态资源
│   ├── App.vue              # 应用入口
│   ├── main.ts              # 主入口文件
│   ├── pages.json           # 页面配置
│   └── manifest.json        # 应用配置
├── package.json
├── tsconfig.json
└── vite.config.ts
```

## 开发指南

### 环境要求
- Node.js >= 16
- pnpm (推荐) 或 npm

### 安装依赖
```bash
cd uniapp
pnpm install
```

### 开发运行
```bash
# H5 开发模式
pnpm dev:h5

# 其他平台
pnpm dev:mp-weixin  # 微信小程序
pnpm dev:app        # App
```

### 构建打包
```bash
# H5 生产构建
pnpm build:h5

# 其他平台构建
pnpm build:mp-weixin
pnpm build:app
```

## API 配置

API 基础地址配置在 `src/utils/api.ts` 中：

```typescript
const BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8080/api'  // 开发环境
  : 'http://localhost:8080/api'  // 生产环境
```

确保后端服务已启动并运行在对应端口。

## 使用流程

### 1. 基础数据准备
1. 进入"教师"页面，添加教师信息和授课科目
2. 进入"学生"页面，添加学生信息
3. 进入"学生组"页面，创建学生组并分配学生

### 2. 创建排课任务
1. 进入"任务"页面，创建新的排课任务
2. 点击任务进入详情页面

### 3. 配置排课参数
1. 在任务详情页面，点击"时间偏好设置"
2. 为教师和学生添加时间偏好
3. 点击"课程需求配置"，设置学生组与教师的课程需求

### 4. 执行排课
1. 在任务详情页面，点击"开始排课"
2. 系统将调用后端排课算法进行计算

### 5. 查看结果
1. 点击"排课结果"查看排课结果
2. 可以查看已排课程和冲突课程
3. 对于冲突课程，系统会提供冲突原因和解决建议

## 主要页面说明

### 首页 (pages/index)
- 系统概览和快捷操作
- 统计信息展示
- 功能模块导航

### 教师管理 (pages/teachers)
- 教师列表展示
- 添加教师功能
- 支持多科目设置

### 学生管理 (pages/students)
- 学生列表展示
- 添加学生功能

### 学生组管理 (pages/groups)
- 学生组列表展示
- 创建学生组
- 学生分配功能

### 排课任务 (pages/tasks)
- 任务列表和状态管理
- 任务详情页面
- 排课参数配置

### 时间偏好 (pages/preferences)
- 教师/学生时间偏好设置
- 优先级管理
- 时间段配置

### 排课结果 (pages/schedules)
- 排课结果展示
- 冲突分析
- 解决方案建议

## 注意事项

1. **网络配置**: 确保 H5 端能够访问后端 API 地址
2. **跨域问题**: 后端已配置 CORS，支持跨域访问
3. **数据同步**: 前端数据与后端实时同步，建议在操作前刷新数据
4. **错误处理**: 系统已集成错误提示，操作失败时会显示具体错误信息

## 扩展开发

### 添加新页面
1. 在 `src/pages/` 下创建新的页面目录
2. 在 `pages.json` 中注册新页面
3. 如需要，在 `tabBar` 中添加导航

### 添加新 API
1. 在 `src/utils/api.ts` 中添加新的接口定义
2. 更新相关的 TypeScript 类型定义
3. 在页面中调用新的 API 方法

### 样式定制
- 全局样式在 `src/uni.scss` 中定义
- 页面样式使用 scoped CSS
- 支持 rpx 响应式单位

## 故障排除

### 常见问题
1. **API 请求失败**: 检查后端服务是否启动，网络是否连通
2. **页面跳转失败**: 检查 `pages.json` 中的路径配置
3. **样式显示异常**: 检查 CSS 语法和 rpx 单位使用

### 调试方法
1. 使用浏览器开发者工具查看网络请求
2. 查看控制台错误信息
3. 使用 UniApp 开发者工具进行调试
