{"ALL_COMPILER_OPTIONS_6917": "ВСЕ ПАРАМЕТРЫ КОМПИЛЯТОРОВ", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Модифик<PERSON><PERSON><PERSON><PERSON> \"{0}\" не может быть использован с объявлением импорта.", "A_0_parameter_must_be_the_first_parameter_2680": "В качестве первого параметра необходимо указать \"{0}\".", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Комментарий \"@typedef\" JSDoc не может содержать несколько тегов \"@type\".", "A_bigint_literal_cannot_use_exponential_notation_1352": "Литерал типа bigint не может использовать экспоненциальное представление.", "A_bigint_literal_must_be_an_integer_1353": "Литерал типа bigint должен быть целым числом.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Параметр шаблона привязки не может быть необязательным в сигнатуре реализации.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Оператор break можно использовать только во включающей итерации или операторе switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Оператор break может переходить только к метке внешнего оператора.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Класс может реализовать только идентификатор или полное имя с дополнительными аргументами типа.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Класс может реализовать только тип объекта или пересечение типов объектов со статическими известными членами.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Объявление класса без модификатора \"default\" должно иметь имя.", "A_class_member_cannot_have_the_0_keyword_1248": "Элемент класса не может иметь ключевое слово \"{0}\".", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Выражение с запятой запрещено в имени вычисляемого свойства.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Имя вычисляемого свойства не может ссылаться на параметр типа из содержащего его типа.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Имя вычисляемого свойства в объявлении свойства класса должно иметь тип простого литерала или тип \"уникальный символ\".", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Имя вычисляемого свойства в перегрузке метода должно ссылаться на выражение, тип которого — литерал или \"unique symbol\".", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Имя вычисляемого свойства в литерале должно ссылаться на выражение, тип которого — литерал или \"unique symbol\".", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Имя вычисляемого свойства в окружающем контексте должно ссылаться на выражение, тип которого — литерал или \"unique symbol\".", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Имя вычисляемого свойства в интерфейсе должно ссылаться на выражение, тип которого — литерал или \"unique symbol\".", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Имя вычисляемого свойства должно иметь тип string, number, symbol или any.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Утверждения \"const\" могут применяться только к ссылкам на члены перечисления либо к строковым, числовым, логическим литералам, литералам массивов или объектов.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Доступ к элементу перечисления констант может осуществляться только с использованием строкового литерала.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Инициализатор \"const\" во внешнем контексте должен быть строкой или числовым литералом либо ссылкой на литеральное перечисление.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Конструктор не может содержать вызов \"super\", если его класс расширяет \"null\".", "A_constructor_cannot_have_a_this_parameter_2681": "Конструктор не может иметь параметр this.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Оператор continue можно использовать только в операторе включающей итерации.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Оператор continue может переходить только к метке оператора включающей итерации.", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Модификатор declare нельзя использовать в уже окружающем контексте.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Декоратор может только декорировать реализацию метода, а не перегрузку.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Предложение default не может повторяться в операторе switch.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Экспорт по умолчанию можно использовать только в модуле в стиле ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Экспорт по умолчанию должен находиться на верхнем уровне объявления файла или модуля.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Утверждение определенного назначения \"!\" запрещено в этом контексте.", "A_destructuring_declaration_must_have_an_initializer_1182": "Объявление деструктурирования должно включать инициализатор.", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "Для вызова динамического импорта в ES5/ES3 требуется конструктор \"Promise\". Убедитесь в наличии объявления для конструктора \"Promise\" или включите \"ES2015\" в параметр \"--lib\".", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Вызов динамического импорта возвращает \"Promise\". Убедитесь в наличии объявления для \"Promise\" или включите \"ES2015\" в параметр \"--lib\".", "A_file_cannot_have_a_reference_to_itself_1006": "Файл не может содержать ссылку на самого себя.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Функция, возвращающая \"never\", не может иметь доступную конечную точку.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Функция, которая вызывается с ключевым словом new, не может иметь тип this со значением void.", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "Функция, объявленный тип которой не является void или any, должна возвращать значение.", "A_generator_cannot_have_a_void_type_annotation_2505": "Генератор не может иметь аннотацию типа void.", "A_get_accessor_cannot_have_parameters_1054": "Метод доступа get не может иметь параметров.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Метод доступа get должен быть доступным как минимум в той же мере, что и метод задания.", "A_get_accessor_must_return_a_value_2378": "Метод доступа get должен возвращать значение.", "A_label_is_not_allowed_here_1344": "Метка здесь запрещена.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Элемент маркированного кортежа объявлен как необязательный с вопросительным знаком между именем и двоеточием, а не после типа.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Маркированный элемент кортежа объявлен как rest с многоточием перед именем, а не перед типом.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Сопоставленный тип не может объявлять свойства и методы.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Инициализатор элемента в объявлении перечисления не может ссылаться на элементы, объявленные после него, включая элементы, определенные в других перечислениях.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Класс примеси должен иметь конструктор с одиночным параметром REST типа any[].", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Класс примеси, который наследуется от переменной типа, содержащей сигнатуру абстрактной конструкции, должен быть также объявлен как \"abstract\".", "A_module_cannot_have_multiple_default_exports_2528": "Модуль не может иметь несколько импортов по умолчанию.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Объявление пространства имен и класс или функция, с которыми оно объединено, не могут находится в разных файлах.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Объявление пространства имен не может располагаться раньше класса или функции, с которыми оно объединено.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Объявление пространства имен разрешено только на верхнем уровне пространства имен или модуля.", "A_non_dry_build_would_build_project_0_6357": "При сборке без флага -dry будет собран проект \"{0}\"", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "При сборке без флага -dry будут удалены следующие файлы: {0}", "A_non_dry_build_would_update_output_of_project_0_6375": "Сборка без флага -dry обновит выходные данные проекта \"{0}\"", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Сборка без флага -dry обновит метки времени для выходных данных проекта \"{0}\"", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Инициализатор параметра разрешено использовать только в реализации функции или конструктора.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Свойство параметра невозможно объявить с помощью параметра REST.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Свойство параметра допускается только в реализации конструктора.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Свойство параметра невозможно объявить с помощью шаблона привязки.", "A_promise_must_have_a_then_method_1059": "Кла<PERSON><PERSON> promise должен содержать метод then.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Свойство класса, тип которого — \"unique symbol\", должно быть задано как \"static\" и \"readonly\".", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Свойство интерфейса или литерала, тип которого — \"unique symbol\", должно быть задано как \"readonly\".", "A_required_element_cannot_follow_an_optional_element_1257": "Обязательный элемент не должен следовать за необязательным.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Обязательный параметр не должен следовать за необязательным.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Элемент rest не может содержать шаблон привязки.", "A_rest_element_cannot_follow_another_rest_element_1265": "Элемент rest не может следовать за другим элементом rest.", "A_rest_element_cannot_have_a_property_name_2566": "Элемент rest не может иметь имя свойства.", "A_rest_element_cannot_have_an_initializer_1186": "Элемент rest не может содержать инициализатор.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Элемент REST должен быть последним в шаблоне деструктуризации.", "A_rest_element_type_must_be_an_array_type_2574": "Элемент rest должен иметь тип массива.", "A_rest_parameter_cannot_be_optional_1047": "Параметр rest не может быть необязательным.", "A_rest_parameter_cannot_have_an_initializer_1048": "Параметр rest не может иметь инициализатор.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Параметр rest должен стоять на последнем месте в списке параметров.", "A_rest_parameter_must_be_of_an_array_type_2370": "Параметр rest должен иметь тип массива.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "После параметра rest или шаблона привязки не может стоять запятая.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Оператор return можно использовать только в теле функции.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Оператор return нельзя использовать внутри статического блока класса.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Серия записей, которая повторно сопоставляет импорты с расположениями поиска, заданными относительно baseUrl.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Метод доступа set не может иметь заметку с типом возвращаемого значения.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Метод доступа set не может иметь необязательный параметр.", "A_set_accessor_cannot_have_rest_parameter_1053": "Метод доступа set не может иметь параметр rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "У метода доступа set должен быть ровно один параметр.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Параметр метода доступа set не может иметь инициализатор.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Аргумент расширения должен иметь тип кортежа либо передаваться в параметр rest.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Вызов \"super\" должен быть инструкцией на корневом уровне в конструкторе производного класса с инициализированными свойствами, свойствами параметров или частными идентификаторами.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Вызов \"super\" должен быть первой инструкцией в конструкторе, ссылающейся на \"super\" или \"this\", если производный класс содержит инициализированные свойства, свойства параметров или частные идентификаторы.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Условие типа this несовместимо с условием типа на основе параметров.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Тип this доступен только в нестатическом элементе класса или интерфейса.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Файл tsconfig.json уже определен в \"{0}\".", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Элемент кортежа не может быть одновременно элементом rest и необязательным элементом.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Тип кортежа нельзя индексировать с отрицательным значением.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Выражение утверждения типа не допускается в левой части выражения, возводимого в степень. Попробуйте заключить выражение в скобки.", "A_type_literal_property_cannot_have_an_initializer_1247": "Свойство литерала типа не может иметь инициализатор.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Импорт, затрагивающий только тип, может указывать либо только импорт по умолчанию, либо только именованные привязки.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Предикат типов не может ссылаться на параметр rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Предикат типов не может ссылаться на элемент \"{0}\" в шаблоне привязки.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Предикат типов разрешено использовать только в позиции типа возвращаемого значения для функций и методов.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Тип предиката типа должен быть доступным для назначения этому типу параметра.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Тип, указанный в декорированной сигнатуре, должен импортироваться с \"import type\" или импортом пространства имен, если включены параметры \"isolatedModules\" и \"emitDecoratorMetadata\".", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Переменная, тип которой — \"unique symbol\", должна быть задана как \"const\".", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Выражение yield разрешено использовать только в теле генератора.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "Невозможно получить доступ к абстрактному методу \"{0}\" класса \"{1}\" с помощью выражения super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Абстрактные методы могут использоваться только в абстрактных классах.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "Абстрактное свойство \"{0}\" в классе \"{1}\" недоступно в конструкторе.", "Accessibility_modifier_already_seen_1028": "Модификатор специальных возможностей уже встречался.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Методы доступа доступны только при разработке для ECMAScript 5 и более поздних версий.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Методы доступа должны быть абстрактными или неабстрактными.", "Add_0_to_unresolved_variable_90008": "Добавить \"{0}.\" к неразрешенной переменной", "Add_a_return_statement_95111": "Добавить оператор return", "Add_all_missing_async_modifiers_95041": "Добавить все отсутствующие модификаторы \"async\"", "Add_all_missing_attributes_95168": "Добавить все отсутствующие атрибуты", "Add_all_missing_call_parentheses_95068": "Добавить все недостающие скобки вызова", "Add_all_missing_function_declarations_95157": "Добавить все недостающие объявления функций", "Add_all_missing_imports_95064": "Добавить все отсутствующие импорты", "Add_all_missing_members_95022": "Добавить все отсутствующие элементы", "Add_all_missing_override_modifiers_95162": "Добавьте все отсутствующие модификаторы \"override\".", "Add_all_missing_properties_95166": "Добавить все отсутствующие свойства", "Add_all_missing_return_statement_95114": "Добавить все отсутствующие операторы return", "Add_all_missing_super_calls_95039": "Добавить все отсутствующие вызовы super", "Add_async_modifier_to_containing_function_90029": "Добавьте модификатор async в содержащую функцию", "Add_await_95083": "Добавить \"await\"", "Add_await_to_initializer_for_0_95084": "Добави<PERSON><PERSON> \"await\" к инициализатору для \"{0}\"", "Add_await_to_initializers_95089": "Доба<PERSON><PERSON><PERSON><PERSON> \"await\" к инициализаторам", "Add_braces_to_arrow_function_95059": "Добавить скобки в стрелочную функцию", "Add_const_to_all_unresolved_variables_95082": "Добавить \"const\" ко всем неразрешенным переменным", "Add_const_to_unresolved_variable_95081": "Добавить \"const\" к неразрешенной переменной", "Add_definite_assignment_assertion_to_property_0_95020": "Добавить утверждение определенного назначения к свойству \"{0}\"", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Добавить утверждения определенного назначения ко всем неинициализированным свойствам", "Add_export_to_make_this_file_into_a_module_95097": "Добавить \"export {}\", чтобы превратить этот файл в модуль", "Add_extends_constraint_2211": "Добавить ограничение \"extends\".", "Add_extends_constraint_to_all_type_parameters_2212": "Добавить ограничение \"extends\" ко всем параметрам типа", "Add_import_from_0_90057": "Добавить импортировать из \"{0}\"", "Add_index_signature_for_property_0_90017": "Добавьте сигнатуру индекса для свойства \"{0}\"", "Add_initializer_to_property_0_95019": "Добавить инициализатор к свойству \"{0}\"", "Add_initializers_to_all_uninitialized_properties_95027": "Добавить инициализаторы ко всем неинициализированным свойствам", "Add_missing_attributes_95167": "Добавить отсутствующие атрибуты", "Add_missing_call_parentheses_95067": "Добавить недостающие скобки вызова", "Add_missing_enum_member_0_95063": "Добавить отсутствующий член перечисления \"{0}\"", "Add_missing_function_declaration_0_95156": "Добавить недостающее объявление функции \"{0}\"", "Add_missing_new_operator_to_all_calls_95072": "Добавить отсутствующий оператор \"new\" во все вызовы", "Add_missing_new_operator_to_call_95071": "Добавить отсутствующий оператор \"new\" в вызов", "Add_missing_properties_95165": "Добавить отсутствующие свойства", "Add_missing_super_call_90001": "Добавьте отсутствующий вызов \"super()\"", "Add_missing_typeof_95052": "Добавить отсутствующий \"typeof\"", "Add_names_to_all_parameters_without_names_95073": "Добавить имена ко всем параметрам без имен", "Add_or_remove_braces_in_an_arrow_function_95058": "Добавить скобки в стрелочную функцию или удалить скобки из нее", "Add_override_modifier_95160": "Добавьте модификатор \"override\".", "Add_parameter_name_90034": "Добавить имя параметра", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Добавить квалификатор ко всем неразрешенным переменным, соответствующим имени члена", "Add_to_all_uncalled_decorators_95044": "Добавить \"()\" ко всем невызванным декораторам", "Add_ts_ignore_to_all_error_messages_95042": "Добавить \"@ts-ignore\" ко всем сообщениям об ошибках", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Добавьте \"undefined\" к типу при доступе с использованием индекса.", "Add_undefined_to_optional_property_type_95169": "Добавить \"undefined\" к необязательному типу свойства", "Add_undefined_type_to_all_uninitialized_properties_95029": "Добавить неопределенный тип ко всем неинициализированным свойствам", "Add_undefined_type_to_property_0_95018": "Добавить тип \"undefined\" к свойству \"{0}\"", "Add_unknown_conversion_for_non_overlapping_types_95069": "Добавить преобразование \"unknown\" для неперекрывающихся типов", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Добавить \"unknown\" для всех преобразований неперекрывающихся типов", "Add_void_to_Promise_resolved_without_a_value_95143": "Добавить \"void\" в Promise (обещание), разрешенное без значения", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Добавить \"void\" во все Promise (обещания), разрешенные без значения", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Добавление файла tsconfig.json поможет организовать проекты, содержащие файлы TypeScript и JavaScript. Дополнительные сведения: https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Все объявления \"{0}\" должны иметь одинаковые ограничения.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Все объявления \"{0}\" должны иметь одинаковые модификаторы.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Все объявления \"{0}\" должны иметь одинаковые параметры типа.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Все объявления абстрактных методов должны быть последовательными.", "All_destructured_elements_are_unused_6198": "Все деструктурированные элементы не используются.", "All_imports_in_import_declaration_are_unused_6192": "Ни один из импортов в объявлении импорта не используется.", "All_type_parameters_are_unused_6205": "Ни один из параметров типа не используется.", "All_variables_are_unused_6199": "Ни одна переменная не используется.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Разрешите файлам JavaScript быть частью программы. Используйте параметр \"checkJS\" для получения ошибок из этих файлов.", "Allow_accessing_UMD_globals_from_modules_6602": "Разрешение обращения к глобальным значениям UMD из модулей.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Разрешить импорт по умолчанию из модулей без экспорта по умолчанию. Это не повлияет на выведение кода — только на проверку типов.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Разрешить \"импортировать x из y\", если модуль не имеет экспорта по умолчанию.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Разрешить импортировать вспомогательныхе функции из tslib один раз для каждого проекта, а не включать их для каждого файла.", "Allow_javascript_files_to_be_compiled_6102": "Разрешить компиляцию файлов javascript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Разрешить обрабатывать несколько папок как одну при разрешении модулей.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Уже включенное имя файла \"{0}\" отличается от имени файла \"{1}\" только регистром.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Объявление окружающего модуля не может содержать относительное имя модуля.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Внешний модуль не может быть вложен в другие модули или пространства имен.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Модуль AMD не может иметь несколько назначений имен.", "An_abstract_accessor_cannot_have_an_implementation_1318": "У абстрактного метода доступа не может быть реализации.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Модификатор специальных возможностей запрещено использовать с закрытым идентификатором.", "An_accessor_cannot_have_type_parameters_1094": "Метод доступа не может иметь параметры типа.", "An_accessor_property_cannot_be_declared_optional_1276": "Невозможно объявить свойство \"accessor\" как необязательное.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Объявление окружающего модуля разрешено использовать только в рамках верхнего уровня файла.", "An_argument_for_0_was_not_provided_6210": "Не указан аргумент для \"{0}\".", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Не указан аргумент, соответствующий этому шаблону привязки.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Арифметический операнд должен иметь тип \"any\", \"number\", \"bigint\" или тип перечисления.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Стрелочная функция не может иметь параметр \"this\".", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "Асинхронной функции или методу ES5/ES3 требуется конструктор \"Promise\". Убедитесь в наличии объявления для конструктора \"Promise\" или включите \"ES2015\" в параметр \"--lib\".", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Асинхронная функция или метод должны вернуть \"Promise\". Убедитесь в наличии объявления для \"Promise\" или включите \"ES2015\" в параметр \"--lib\".", "An_async_iterator_must_have_a_next_method_2519": "В асинхронном итераторе должен быть метод next().", "An_element_access_expression_should_take_an_argument_1011": "Выражение доступа к элементу должно принимать аргумент.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Член перечисления не может иметь имя с закрытым идентификатором.", "An_enum_member_cannot_have_a_numeric_name_2452": "Имя элемента перечисления не может быть числовым.", "An_enum_member_name_must_be_followed_by_a_or_1357": "После имени члена перечисления должен стоять знак \",\", \"=\" или \"}\".", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Расширенная версия этих сведений, в которой показаны все возможные параметры компиляторов", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Назначение экспорта нельзя использовать в модуле с другими экспортированными элементами.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Назначение экспорта нельзя использовать в пространстве имен.", "An_export_assignment_cannot_have_modifiers_1120": "Назначение экспорта не может иметь модификаторы.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Назначение экспорта должно находиться на верхнем уровне объявления файла или модуля.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Объявление экспорта может использоваться только на верхнем уровне модуля.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Объявление экспорта может использоваться только на верхнем уровне пространства имен или модуля.", "An_export_declaration_cannot_have_modifiers_1193": "Объявление экспорта не может иметь модификаторы.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "Выражение типа \"void\" не может быть проверено на истинность.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Расширенное escape-значение в Юникоде должно быть в пределах от 0x0 до 0x10FFFF включительно.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Идентификатор или ключевое слово не может следовать непосредственно за числовым литералом.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Реализацию невозможно объявить в окружающих контекстах.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Псевдоним импорта не может ссылаться на объявление, экспортированное с помощью \"export type\".", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Псевдоним импорта не может ссылаться на объявление, импортированное с помощью \"import type\".", "An_import_alias_cannot_use_import_type_1392": "Псевдоним импорта не может использовать \"import type\".", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Объявление импорта может использоваться только на верхнем уровне модуля.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Объявление импорта может использоваться только на верхнем уровне пространства имен или модуля.", "An_import_declaration_cannot_have_modifiers_1191": "Объявление импорта не может иметь модификаторы.", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "Путь импорта не может заканчиваться расширением \"{0}\". Попробуйте импортировать \"{1}\".", "An_index_signature_cannot_have_a_rest_parameter_1017": "Сигнатура индекса не может иметь параметр rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Сигнатура индекса не может заканчиваться запятой.", "An_index_signature_must_have_a_type_annotation_1021": "У сигнатуры индекса должна быть аннотация типа.", "An_index_signature_must_have_exactly_one_parameter_1096": "У сигнатуры индекса должен быть ровно один параметр.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Параметр сигнатуры индекса не может содержать вопросительный знак.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Параметра сигнатуры индекса не может содержать модификатор специальных возможностей.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Параметр сигнатуры индекса не может содержать инициализатор.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "У параметра сигнатуры индекса должна быть аннотация типа.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Тип параметра сигнатуры индекса не может быть типом литерала или универсальным типом. Рекомендуется использовать тип сопоставляемого объекта.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Тип параметра сигнатуры индекса должен быть строкой, числом, символом или типом литерала шаблона.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "За выражением создания экземпляра не может следовать доступ к свойству.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Интерфейс может расширить только идентификатор или полное имя с дополнительными аргументами типа.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Интерфейс может расширять только тип объекта или пересечение типов объектов со статическими известными членами.", "An_interface_cannot_extend_a_primitive_type_like_0_an_interface_can_only_extend_named_types_and_clas_2840": "Интерфейс не может расширять примитивный тип, например \"{0}\". Интерфейс может расширять только именованные типы и классы", "An_interface_property_cannot_have_an_initializer_1246": "Свойство интерфейса не может иметь инициализатор.", "An_iterator_must_have_a_next_method_2489": "Итератор должен иметь метод \"next()\".", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "При использовании директивы pragma @jsx с фрагментами JSX требуется директива pragma @jsxFrag.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Объектный литерал не может иметь несколько методов доступа get/set с одинаковым именем.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "У объектного литерала не может быть несколько свойств с одинаковым именем.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Объектный литерал не может иметь свойство и метод доступа с одинаковым именем.", "An_object_member_cannot_be_declared_optional_1162": "Элемент объекта не может быть объявлен необязательным.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Необязательная цепочка не может содержать закрытые идентификаторы.", "An_optional_element_cannot_follow_a_rest_element_1266": "Необязательный элемент не может следовать за элементом rest.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Этот контейнер затемняет внешнее значение \"this\".", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Сигнатура перегрузки не может быть объявлена в качестве генератора.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Унарное выражение с оператором \"{0}\" не допускается в левой части выражения, возводимого в степень. Попробуйте заключить выражение в скобки.", "Annotate_everything_with_types_from_JSDoc_95043": "Добавить заметки ко всем элементам с типами JSDoc", "Annotate_with_type_from_JSDoc_95009": "Заметка с типом из JSDoc", "Another_export_default_is_here_2753": "Здесь находится другой экспорт данных по умолчанию.", "Are_you_missing_a_semicolon_2734": "У вас отсутствует точка с запятой?", "Argument_expression_expected_1135": "Ожидалось выражение аргумента.", "Argument_for_0_option_must_be_Colon_1_6046": "Аргумент для параметра \"{0}\" должен быть {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "Аргумент динамического импорта не может быть элементом расширения.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "Аргумент типа \"{0}\" нельзя назначить параметру типа \"{1}\".", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "Невозможно назначить аргумент типа \"{0}\" параметру типа \"{1}\", когда свойство \"exactOptionalPropertyTypes\" имеет значение \"true\". Рассмотрите возможность добавления типа \"undefined\" к типам свойств цели.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "Не указаны аргументы для параметра REST \"{0}\".", "Array_element_destructuring_pattern_expected_1181": "Ожидался шаблон деструктурирования элемента массива.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Утверждения требуют, чтобы каждое имя в целевом объекте вызова было объявлено с явной заметкой с типом.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Утверждения требуют, чтобы целевой объект вызова был идентификатором или полным именем.", "Asterisk_Slash_expected_1010": "Ожидалось \"*/\".", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Улучшения для глобальной области могут быть вложены во внешние модули или неоднозначные объявления модулей только напрямую.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Улучшения для глобальной области не должны иметь модификатор declare, если они отображаются в окружающем контексте.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "Автообнаружение для вводимых данных включено в проекте \"{0}\". Идет запуск дополнительного этапа разрешения для модуля \"{1}\" с использованием расположения кэша \"{2}\".", "Await_expression_cannot_be_used_inside_a_class_static_block_18037": "Выражение Await нельзя использовать внутри статического блока класса.", "BUILD_OPTIONS_6919": "ПАРАМЕТРЫ СБОРКИ", "Backwards_Compatibility_6253": "Обратная совместимость", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Выражения базового класса не могут ссылаться на параметры типа класса.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Тип возвращаемого значения базового конструктора \"{0}\" не является ни типом объекта, ни пересечением типов объектов со статическими известными членами.", "Base_constructors_must_all_have_the_same_return_type_2510": "Конструкторы базового класса должны иметь одинаковые типы возвращаемых значений.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Базовый каталог для разрешения неабсолютных имен модуля.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "Литералы типа bigint недоступны при нацеливании на версию ниже ES2020.", "Binary_digit_expected_1177": "Ожида<PERSON><PERSON>я бит.", "Binding_element_0_implicitly_has_an_1_type_7031": "Элемент привязки \"{0}\" имеет неявный тип \"{1}\".", "Block_scoped_variable_0_used_before_its_declaration_2448": "Переменная \"{0}\" с областью видимости, ограниченной блоком, использована перед своим объявлением.", "Build_a_composite_project_in_the_working_directory_6925": "Создание составного проекта в рабочей папке.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Собирайте все проекты, включая не требующие обновления.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Собрать один проект или несколько и их зависимости, если они не обновлены", "Build_option_0_requires_a_value_of_type_1_5073": "Параметр сборки \"{0}\" требует значение типа {1}.", "Building_project_0_6358": "Сборка проекта \"{0}\"...", "COMMAND_LINE_FLAGS_6921": "ФЛАГИ КОМАНДНОЙ СТРОКИ", "COMMON_COMMANDS_6916": "ОБЩИЕ КОМАНДЫ", "COMMON_COMPILER_OPTIONS_6920": "ОБЩИЕ ПАРАМЕТРЫ КОМПИЛЯТОРОВ", "Call_decorator_expression_90028": "Вызовите выражение декоратора", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Типы возвращаемых значений сигнатур вызовов \"{0}\" и \"{1}\" несовместимы.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Сигнатура вызова, у которой нет аннотации типа возвращаемого значения, неявно имеет тип возвращаемого значения any.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Сигнатуры вызова без аргументов имеют несовместимые типы возвращаемых значений \"{0}\" и \"{1}\".", "Call_target_does_not_contain_any_signatures_2346": "Объект вызова не содержит сигнатуры.", "Can_only_convert_logical_AND_access_chains_95142": "Можно преобразовывать только цепочки доступа с логическим И.", "Can_only_convert_named_export_95164": "Можно преобразовать только именованный экспорт.", "Can_only_convert_property_with_modifier_95137": "Можно только преобразовать свойство с модификатором", "Can_only_convert_string_concatenation_95154": "Можно только преобразовать объединение строк.", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Не удается получить доступ к {0}.{1}, так как {0} является типом, но не является пространством имен. Вы хотели получить тип свойства {1} в {0} с использованием {0}[\"{1}\"]?", "Cannot_access_ambient_const_enums_when_the_isolatedModules_flag_is_provided_2748": "Не удается обратиться к перечислениям внешних констант, если задан флаг \"--isolatedModules\".", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Не удается назначить тип конструктора \"{0}\" для типа конструктора \"{1}\".", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Не удается назначить тип конструктора абстрактного класса для типа конструктора класса, не являющегося абстрактным.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Не удается задать значение для \"{0}\", так как это класс.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Не удается задать значение для \"{0}\", так как это константа.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Не удается задать значение для \"{0}\", так как это функция.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Не удается задать значение для \"{0}\", так как это пространство имен.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Не удается задать значение для \"{0}\", так как это свойство, доступное только для чтения.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Не удается задать значение для \"{0}\", так как это перечисление.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Не удается задать значение для \"{0}\", так как это импорт.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Не удается задать значение для \"{0}\", так как это не переменная.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Не удается присвоить значение частному методу \"{0}\". Частные методы недоступны для записи.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Не удается улучшить модуль \"{0}\", так как он разрешается в немодульную сущность.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Невозможно добавить экспорт значений в модуль \"{0}\", так как он разрешается в немодульную сущность.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Невозможно скомпилировать модули с использованием параметра \"{0}\", если флаг \"--module\" не имеет значения \"amd\" или \"system\".", "Cannot_create_an_instance_of_an_abstract_class_2511": "Невозможно создать экземпляр абстрактного класса.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Не удается делегировать итерацию значению, так как метод \"next\" его итератора ожидает тип \"{1}\", но содержащий генератор всегда будет отправлять \"{0}\".", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Не удается экспортировать \"{0}\". Только локальные объявления можно экспортировать из модуля.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Не удается расширить класс \"{0}\". Конструктор класса помечен как частный.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Не удается расширить интерфейс \"{0}\". Вы имели в виду \"реализует\"?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Не удается найти файл tsconfig.json в текущем каталоге: {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Не удается найти файл tsconfig.json в указанном каталоге: \"{0}\".", "Cannot_find_global_type_0_2318": "Не удается найти глобальный тип \"{0}\".", "Cannot_find_global_value_0_2468": "Не удается найти глобальное значение \"{0}\".", "Cannot_find_lib_definition_for_0_2726": "Не удается найти определение lib для \"{0}\".", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Не удается найти определение lib для \"{0}\". Вы имели в виду \"{1}\"?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Не удается найти модуль \"{0}\". Рекомендуется использовать параметр \"--resolveJsonModule\" для импорта модуля с расширением \".json\".", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_node_or_to_add_aliases_to_th_2792": "Не найден модуль \"{0}\". Хотели ли вы задать значение \"node\" для параметра \"moduleResolution\" или добавить псевдонимы в параметр \"paths\"?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "Не удается найти модуль \"{0}\" или связанные с ним объявления типов.", "Cannot_find_name_0_2304": "Не удается найти имя \"{0}\".", "Cannot_find_name_0_Did_you_mean_1_2552": "Не удается найти имя \"{0}\". Вы имели в виду \"{1}\"?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Не удается найти имя \"{0}\". Возможно, вы имели в виду элемент экземпляра \"this.{0}\"?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Не удается найти имя \"{0}\". Возможно, вы имели в виду статический элемент \"{1}.{0}\"?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "Не удается найти имя \"{0}\". Вы собирались использовать его в асинхронной функции?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Не удается найти имя \"{0}\". Вы хотите изменить целевую библиотеку? Попробуйте изменить параметр компилятора \"lib\" на \"{1}\" или более поздней версии.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Не удается найти имя \"{0}\". Вы хотите изменить целевую библиотеку? Попробуйте изменить параметр компилятора \"lib\", включив \"dom\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для средства запуска тестов? Попробуйте использовать команды \"npm i --save-dev @types/jest\" или \"npm i --save-dev @types/mocha\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для средства выполнения тестов? Попробуйте использовать `npm i --save-dev @types/jest` или `npm i --save-dev @types/mocha`, а затем добавьте \"jest\" или \"mocha\" в поле типов в файле tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для jQuery? Попробуйте использовать команду \"npm i --save-dev @types/jquery\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для jQuery? Попробуйте использовать `npm i --save-dev @types/jquery`, а затем добавить \"jquery\" в поле типов в файле tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для узла? Попробуйте использовать команду \"npm i --save-dev @types/node\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Не удается найти имя \"{0}\". Вы хотите установить определения типов для узла? Попробуйте использовать `npm i --save-dev @types/node`, а затем добавьте \"node\" в поле типов в файле tsconfig.", "Cannot_find_namespace_0_2503": "Не удается найти пространство имен \"{0}\".", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Невозможно найти пространство имен \"{0}\". Вы имели в виду \"{1}\"?", "Cannot_find_parameter_0_1225": "Не удается найти параметр \"{0}\".", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Не удается найти общий путь к подкаталогу для входных файлов.", "Cannot_find_type_definition_file_for_0_2688": "Не удается найти файл определения типа для \"{0}\".", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Невозможно импортировать файлы объявления типа. Рекомендуется импортировать \"{0}\" вместо \"{1}\".", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Невозможно инициализировать переменную \"{0}\" с внешней областью видимости в той же области видимости, что и объявление \"{1}\" с областью видимости \"Блок\".", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Не удается вызвать объект, который может иметь значение \"NULL\".", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Не удается вызвать объект, который может иметь значение \"NULL\" или \"undefined\".", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Не удается вызвать объект, который может иметь значение \"undefined\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Не удается выполнить итерацию по значению, так как метод \"next\" его итератора ожидает тип \"{1}\", но деструктурирование массива всегда будет отправлять \"{0}\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Не удается выполнить итерацию по значению, так как метод \"next\" его итератора ожидает тип \"{1}\", но расширение массива всегда будет отправлять \"{0}\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Не удается выполнить итерацию по значению, так как метод \"next\" его итератора ожидает тип \"{1}\", но \"for-of\" всегда будет отправлять \"{0}\".", "Cannot_prepend_project_0_because_it_does_not_have_outFile_set_6308": "Невозможно добавить проект \"{0}\" в начало, так как для него не задан outFile", "Cannot_read_file_0_5083": "Невозможно прочитать файл \"{0}\".", "Cannot_read_file_0_Colon_1_5012": "Не удается считать файл \"{0}\": {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Невозможно повторно объявить переменную \"{0}\" с областью видимости \"Блок\".", "Cannot_redeclare_exported_variable_0_2323": "Не удается повторно объявить экспортированную переменную \"{0}\".", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Невозможно повторно объявить идентификатор \"{0}\" в операторе catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Не удается запустить вызов функции в заметке типа.", "Cannot_update_output_of_project_0_because_there_was_error_reading_file_1_6376": "Не удается обновить выходные данные проекта \"{0}\", так как произошла ошибка при чтении файла \"{1}\".", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Невозможно использовать JSX, если не задан флаг \"--jsx\".", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_the_isolatedModules_flag_is_provided_1269": "Невозможно использовать \"export import\" для типа или пространства имен, доступного только для типов, когда указан флаг \"--isolatedModules\".", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Невозможно использовать директивы import, export или приращения модуля, если флаг \"--module\" имеет значение \"none\".", "Cannot_use_namespace_0_as_a_type_2709": "Невозможно использовать пространство имен \"{0}\" как тип.", "Cannot_use_namespace_0_as_a_value_2708": "Невозможно использовать пространство имен \"{0}\" как значение.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Нельзя использовать \"this\" в инициализаторе статического свойства для класса, использующего декоратор.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Не удается записать файл \"{0}\", так как это перезапишет файл \"TSBUILDINFO\", созданный проектом \"{1}\", на который указывает ссылка.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Не удается записать файл \"{0}\", так как он будет перезаписан несколькими входными файлами.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Не удается записать файл \"{0}\", так как это привело бы к перезаписи входного файла.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Переменная оператора catch не может иметь инициализатор.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Если аннотация для типа переменной предложения clutch указана, это должна быть аннотация \"any\" или \"unknown\".", "Change_0_to_1_90014": "Измените \"{0}\" на \"{1}\"", "Change_all_extended_interfaces_to_implements_95038": "Изменить все расширенные интерфейсы на \"implements\"", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Изменить все типы JSDoc на TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Изменить все типы JSDoc на TypeScript (и добавить \"| undefined\" к типам, допускающим значение NULL)", "Change_extends_to_implements_90003": "Измените \"extends\" на \"implements\"", "Change_spelling_to_0_90022": "Измените написание на \"{0}\"", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Проверьте свойства класса, которые объявлены, но не заданы в конструкторе.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Убедитесь, что аргументы для методов \"bind\", \"call\" и \"apply\" соответствуют исходной функции.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "Идет проверка того, является ли \"{0}\" самым длинным соответствующим префиксом для \"{1}\" — \"{2}\".", "Circular_definition_of_import_alias_0_2303": "Циклическое определение псевдонима импорта \"{0}\".", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Обнаружена цикличность при разрешении конфигурации: {0}", "Circularity_originates_in_type_at_this_location_2751": "Цикличность происходит из типа в этом расположении.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "К<PERSON>а<PERSON><PERSON> \"{0}\" определяет метод доступа — элемент экземпляра \"{1}\", а расширенный класс \"{2}\" определяет его как функцию — элемент экземпляра.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "К<PERSON>а<PERSON><PERSON> \"{0}\" определяет функцию — элемент экземпляра \"{1}\", а расширенный класс \"{2}\" определяет ее как метод доступа — элемент экземпляра.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "К<PERSON>а<PERSON><PERSON> \"{0}\" определяет свойство-элемент экземпляра \"{1}\", а расширенный класс \"{2}\" определяет его как функцию-элемент экземпляра.", "Class_0_incorrectly_extends_base_class_1_2415": "<PERSON>ла<PERSON><PERSON> \"{0}\" неправильно расширяет базовый класс \"{1}\".", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "Клас<PERSON> \"{0}\" неправильно реализует класс \"{1}\". Вы хотели расширить \"{1}\" и унаследовать его члены в виде подкласса?", "Class_0_incorrectly_implements_interface_1_2420": "<PERSON>ла<PERSON><PERSON> \"{0}\" неправильно реализует интерфейс \"{1}\".", "Class_0_used_before_its_declaration_2449": "Класс \"{0}\" использован прежде, чем объявлен.", "Class_constructor_may_not_be_a_generator_1368": "Конструктор класса не может быть генератором.", "Class_constructor_may_not_be_an_accessor_1341": "Конструктор класса не может быть методом доступа.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "Объявление класса не может реализовать список перегрузок для \"{0}\".", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "В объявлении класса не может использоваться более одного тега \"@augments\" или \"@extends\".", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Декораторы классов не могут использоваться со статическим частным идентификатором. Попробуйте удалить экспериментальный декоратор.", "Class_name_cannot_be_0_2414": "Имя класса не может иметь значение \"{0}\".", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Класс не может иметь имя \"Object\" при выборе ES5 с модулем {0} в качестве целевого.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Статическая сторона класса \"{0}\" неправильно расширяет статическую сторону базового класса \"{1}\".", "Classes_can_only_extend_a_single_class_1174": "Классы могут расширить только один класс.", "Classes_may_not_have_a_field_named_constructor_18006": "Классы не могут иметь поле с именем \"constructor\".", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Код, содержащийся в классе, вычисляется в строгом режиме JavaScript, который не позволяет использовать \"{0}\". Дополнительные сведения см. в https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Параметры командной строки", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Компиляция проекта по заданному пути к файлу конфигурации или папке с файлом tsconfig.json.", "Compiler_Diagnostics_6251": "Диагностика компилятора", "Compiler_option_0_expects_an_argument_6044": "Параметр компилятора \"{0}\" ожидает аргумент.", "Compiler_option_0_may_not_be_used_with_build_5094": "Параметр компилятора \"--{0}\" не может использоваться с \"--build\".", "Compiler_option_0_may_only_be_used_with_build_5093": "Параметр компилятора \"--{0}\" может использоваться только с \"--build\".", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "Параметр компилятора \"{0}\" со значением \"{1}\" нестабилен. Используйте ночную сборку TypeScript, чтобы скрыть эту ошибку. Для обновления попробуйте использовать команду \"npm install -D typescript@next\".", "Compiler_option_0_requires_a_value_of_type_1_5024": "Параметр \"{0}\" компилятора требует значение типа {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Компилятор резервирует имя \"{0}\" при выдаче закрытого идентификатора на нижний уровень.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Компилирует проект TypeScript, расположенный по указанному пути", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Компилирует текущий проект (tsconfig.js в рабочей папке.)", "Compiles_the_current_project_with_additional_settings_6929": "Компилирует текущий проект с дополнительными параметрами", "Completeness_6257": "Полнота", "Composite_projects_may_not_disable_declaration_emit_6304": "Составные проекты не могут отключать выпуск объявления.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Составные проекты не могут отключить добавочную компиляцию.", "Computed_from_the_list_of_input_files_6911": "Вычислено из списка входных файлов", "Computed_property_names_are_not_allowed_in_enums_1164": "Имена вычисляемых свойств запрещены в перечислениях.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Вычисленные значения запрещены в перечислении с членами, имеющими строковые значения.", "Concatenate_and_emit_output_to_single_file_6001": "Связать и вывести результаты в один файл.", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "Конфликтующие определения для \"{0}\" найдены в \"{1}\" и \"{2}\". Попробуйте установить конкретную версию этой библиотеки, чтобы устранить конфликт.", "Conflicts_are_in_this_file_6201": "В этом файле присутствуют конфликты.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Попробуйте добавить к этому классу модификатор \"declare\".", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Типы возвращаемых значений сигнатур конструкции \"{0}\" и \"{1}\" несовместимы.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Сигнатура конструктора, у которой нет аннотации типа возвращаемого значения, неявно имеет тип возвращаемого значения any.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Сигнатуры конструкции без аргументов имеют несовместимые типы возвращаемых значений \"{0}\" и \"{1}\".", "Constructor_implementation_is_missing_2390": "Отсутствует реализация конструктора.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Конструктор класса \"{0}\" является частным и доступным только в объявлении класса.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Конструктор класса \"{0}\" защищен и доступен только в объявлении класса.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "При использовании в типе объединения нотация типа конструктора должна быть заключена в круглые скобки.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "При использовании в типе пересечения нотация типа конструктора должна быть заключена в круглые скобки.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Конструкторы производных классов должны содержать вызов super.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Содержащий файл не указан, корневой каталог невозможно определить. Выполняется пропуск поиска в папке node_modules.", "Containing_function_is_not_an_arrow_function_95128": "Содержащая функция не является стрелочной", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Выбирайте метод, который нужно использовать для обнаружения JS-файлов в формате модуля.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "Преобразование типа \"{0}\" в тип \"{1}\" может привести к ошибке, так как ни один из типов не перекрывается с другим в достаточной степени. Если это сделано намеренно, сначала преобразуйте выражение в \"unknown\".", "Convert_0_to_1_in_0_95003": "Преобразовать \"{0}\" в \"{1} в {0}\"", "Convert_0_to_mapped_object_type_95055": "Преобразовать \"{0}\" в тип сопоставленного объекта", "Convert_all_const_to_let_95102": "Преобразовать все \"const\" в \"let\"", "Convert_all_constructor_functions_to_classes_95045": "Преобразовать все функции конструктора в классы", "Convert_all_imports_not_used_as_a_value_to_type_only_imports_1374": "Преобразовать все импорты, не используемые в качестве значения, в импорты, затрагивающие только тип", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Преобразовать все недопустимые символы в код сущности HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Преобразовать все повторно экспортированные типы в экспорты, затрагивающие только тип", "Convert_all_require_to_import_95048": "Преобразовать все \"require\" в \"import\"", "Convert_all_to_async_functions_95066": "Преобразовать все в асинхронные функции", "Convert_all_to_bigint_numeric_literals_95092": "Преобразовать все в числовые литералы типа bigint", "Convert_all_to_default_imports_95035": "Преобразовать все в импорт по умолчанию", "Convert_all_type_literals_to_mapped_type_95021": "Преобразовать все литералы типов в сопоставленный тип", "Convert_arrow_function_or_function_expression_95122": "Преобразовать стрелочную функцию или выражение функции", "Convert_const_to_let_95093": "Преобразовать \"const\" в \"let\"", "Convert_default_export_to_named_export_95061": "Преобразовать экспорт по умолчанию в именованный экспорт", "Convert_function_declaration_0_to_arrow_function_95106": "Преобразовать объявление функции \"{0}\" в стрелочную функцию", "Convert_function_expression_0_to_arrow_function_95105": "Преобразовать выражение функции \"{0}\" в стрелочную функцию", "Convert_function_to_an_ES2015_class_95001": "Преобразование функции в класс ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Преобразовать недопустимый знак в его код сущности HTML", "Convert_named_export_to_default_export_95062": "Преобразовать именованный экспорт в экспорт по умолчанию", "Convert_named_imports_to_default_import_95170": "Преобразовать именованные операции импорта в стандартный импорт", "Convert_named_imports_to_namespace_import_95057": "Преобразовать операции импорта имен в импорт пространства имен", "Convert_namespace_import_to_named_imports_95056": "Преобразовать импорт пространства имен в операции импорта имен", "Convert_overload_list_to_single_signature_95118": "Преобразовать список перегрузок в одиночную сигнатуру", "Convert_parameters_to_destructured_object_95075": "Преобразовать параметры в деструктурированный объект", "Convert_require_to_import_95047": "Преобразовать \"require\" в \"import\"", "Convert_to_ES_module_95017": "Преобразовать в модуль ES", "Convert_to_a_bigint_numeric_literal_95091": "Преобразовать в числовой литерал типа bigint", "Convert_to_anonymous_function_95123": "Преобразовать в анонимную функцию", "Convert_to_arrow_function_95125": "Преобразовать в стрелочную функцию", "Convert_to_async_function_95065": "Преобразовать в асинхронную функцию", "Convert_to_default_import_95013": "Преобразовать в импорт по умолчанию", "Convert_to_named_function_95124": "Преобразовать в именованную функцию", "Convert_to_optional_chain_expression_95139": "Преобразовать в необязательное выражение цепочки", "Convert_to_template_string_95096": "Преобразовать в строку шаблона", "Convert_to_type_only_export_1364": "Преобразовать в экспорт, распространяющийся только на тип", "Convert_to_type_only_import_1373": "Преобразовать в импорт, распространяющийся только на тип", "Corrupted_locale_file_0_6051": "Поврежденный файл языкового стандарта \"{0}\".", "Could_not_convert_to_anonymous_function_95153": "Не удалось преобразовать в анонимную функцию.", "Could_not_convert_to_arrow_function_95151": "Не удалось преобразовать в стрелочную функцию.", "Could_not_convert_to_named_function_95152": "Не удалось преобразовать в именованную функцию.", "Could_not_determine_function_return_type_95150": "Не удалось определить тип возвращаемого значения функции.", "Could_not_find_a_containing_arrow_function_95127": "Не удалось найти содержащую стрелочную функцию", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Не удалось найти файл объявления модуля \"{0}\". \"{1}\" имеет неявный тип \"any\".", "Could_not_find_convertible_access_expression_95140": "Не удалось найти преобразуемое выражение доступа.", "Could_not_find_export_statement_95129": "Не удалось найти инструкцию экспорта.", "Could_not_find_import_clause_95131": "Не удалось найти предложение импорта.", "Could_not_find_matching_access_expressions_95141": "Не удалось найти соответствующие выражения доступа.", "Could_not_find_name_0_Did_you_mean_1_2570": "Не удалось найти имя \"{0}\". Вы имели в виду \"{1}\"?", "Could_not_find_namespace_import_or_named_imports_95132": "Не удалось найти импорт пространства имен или именованные импорты.", "Could_not_find_property_for_which_to_generate_accessor_95135": "Не удалось найти свойство, для которого создается метод доступа.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Не удалось разрешить путь \"{0}\" с расширениями: {1}.", "Could_not_write_file_0_Colon_1_5033": "Не удалось записать файл \"{0}\": \"{1}\".", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Создать файлы сопоставителя с исходным кодом для выпущенных файлов JavaScript.", "Create_sourcemaps_for_d_ts_files_6614": "Создание сопоставителя с исходным кодом для файлов d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Создает tsconfig.jsс рекомендуемыми параметрами в рабочей папке.", "DIRECTORY_6038": "КАТАЛОГ", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "Объявление дополняет объявление в другом файле. Сериализация невозможна.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Для порождения объявления для этого файла требуется использовать закрытое имя \"{0}\". Явная заметка с типом может разблокировать порождение объявления.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Для порождения объявления для этого файла требуется использовать закрытое имя \"{0}\" из модуля \"{1}\". Я<PERSON>ная заметка с типом может разблокировать порождение объявления.", "Declaration_expected_1146": "Ожидалось объявление.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Имя объявления конфликтует со встроенным глобальным идентификатором \"{0}\".", "Declaration_or_statement_expected_1128": "Ожидалось объявление или оператор.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Ожидалось объявление или инструкция. Этот символ \"=\" следует за блоком инструкций, поэтому, если вы хотите использовать назначение деструктурирования, может потребоваться заключить все присваивание в круглые скобки.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Объявления с определенными утверждениями присваивания также должны иметь заметки типов.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Объявления с инициализаторами не могут также иметь определенные утверждения присваивания.", "Declare_a_private_field_named_0_90053": "Объявите закрытое поле с именем \"{0}\".", "Declare_method_0_90023": "Объявите метод \"{0}\"", "Declare_private_method_0_90038": "Объявление закрытого метода \"{0}\"", "Declare_private_property_0_90035": "Объявление закрытого свойства \"{0}\"", "Declare_property_0_90016": "Объявите свойство \"{0}\"", "Declare_static_method_0_90024": "Объявите статический метод \"{0}\"", "Declare_static_property_0_90027": "Объявите статическое свойство \"{0}\"", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Тип возвращаемого значения функции декоратора \"{0}\" нельзя назначить типу \"{1}\".", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Тип возвращаемого значения функции декоратора — \"{0}\", но ожидается \"void\" или \"any\".", "Decorators_are_not_valid_here_1206": "Декораторы здесь недопустимы.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Декораторы нельзя применять к множественным методам доступа get или set с совпадающим именем.", "Decorators_may_not_be_applied_to_this_parameters_1433": "Декораторы не могут применяться к параметрам \"this\".", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Перед именем и всеми ключевыми словами объявлений свойств должны предшествовать декораторы.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "По умолчанию применяйте для переменных предложения catch значение \"unknown\" вместо \"any\".", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Экспорт модуля по умолчанию использует или имеет закрытое имя \"{0}\".", "Default_library_1424": "Библиотека по умолчанию", "Default_library_for_target_0_1425": "Библиотека по умолчанию для целевого объекта \"{0}\"", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Определения следующих идентификаторов конфликтуют с определениями в другом файле: {0}", "Delete_all_unused_declarations_95024": "Удалить все неиспользуемые объявления", "Delete_all_unused_imports_95147": "Удаление всех неиспользуемых импортов", "Delete_all_unused_param_tags_95172": "Удалить все неиспользуемые теги \"@param\"", "Delete_the_outputs_of_all_projects_6365": "Удалите выходные данные всех проектов.", "Delete_unused_param_tag_0_95171": "Удалить неиспользуемый тег \"@param\" \"{0}\"", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Устарело.] Используйте --jsxFactory. Укажите объект, вызываемый для createElement при целевом порождении JSX react", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Устарело.] Используйте --outFile. Сцепление и порождение выходных данных в одном файле", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Устарело.] Используйте --skipLibCheck. Пропуск проверки типов для файлов объявления библиотеки по умолчанию.", "Deprecated_setting_Use_outFile_instead_6677": "Устаревший параметр. Используйте вместо этого \"outFile\".", "Did_you_forget_to_use_await_2773": "Возможно, пропущено \"await\"?", "Did_you_mean_0_1369": "Вы хотели использовать \"{0}\"?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "Вы хотели, чтобы \"{0}\" был ограничен типом \"new (...args: any[]) => {1}\"?", "Did_you_mean_to_call_this_expression_6212": "Вы хотели вызвать это выражение?", "Did_you_mean_to_mark_this_function_as_async_1356": "Вы хотели пометить эту функцию как \"async\"?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Вы хотели использовать \":\"? Знак равенства \"=\" после имени свойства может указываться только в том случае, если внутренний объектный литерал является частью шаблона деструктурирования.", "Did_you_mean_to_use_new_with_this_expression_6213": "Вы хотели использовать \"new\" с этим выражением?", "Digit_expected_1124": "Ожидалась цифра.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Каталог<PERSON> \"{0}\" не существует. Поиск в нем будет пропускаться.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "Ката<PERSON>ог \"{0}\" не имеет содержащей области package.json. Импорт не удастся разрешить.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Отключить добавление директив \"use strict\" в создаваемых файлах JavaScript.", "Disable_checking_for_this_file_90018": "Отключите проверку для этого файла", "Disable_emitting_comments_6688": "Отключить создаваемые комментарии.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Отключите отправку объявлений, в комментариях JSDoc которых есть \"@internal\".", "Disable_emitting_files_from_a_compilation_6660": "Отключите создание файлов при компиляции.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Отключить создаваемый файл, если сообщается об ошибках проверки типов.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Отключите стирание объявлений \"const enum\" в сгенерированном коде.", "Disable_error_reporting_for_unreachable_code_6603": "Отключить отчеты об ошибках для недостижимого кода.", "Disable_error_reporting_for_unused_labels_6604": "Отключить отчеты об ошибках для неиспользуемых меток.", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Отключите создание пользовательских вспомогательных функций, таких как \"__extends\", в скомпилированных выходных данных.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Отключить включение любых файлов библиотеки, включая lib.d.ts по умолчанию.", "Disable_loading_referenced_projects_6235": "Отключите загрузку проектов, на которые имеются ссылки.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Отключите предпочтение исходных файлов вместо файлов объявлений при обращении к составным проектам.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Отключить отправку отчетов об избыточных ошибках свойств во время создания объектных литералов.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Отключить разрешение символических ссылок на их реальный путь. Это соответствует тому же флажку в узле.", "Disable_size_limitations_on_JavaScript_projects_6162": "Отключение ограничений на размеры в проектах JavaScript.", "Disable_solution_searching_for_this_project_6224": "Отключите поиск решений для этого проекта.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Отключить строгую проверку универсальных сигнатур в типах функций.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Отключить получение типа для проектов JavaScript", "Disable_truncating_types_in_error_messages_6663": "Отключить усечение типов в сообщениях об ошибках.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Отключите использование исходных файлов вместо файлов объявлений из проектов, указанных в ссылках.", "Disable_wiping_the_console_in_watch_mode_6684": "Отключите очистку консоли в режиме просмотра.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Отключает вывод для получения типа при просмотре имен файлов в проекте.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Запретите \"import\", \"require\" или \"<reference>\" увеличивать количество файлов, которые TypeScript должен добавить в проект.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Запретить ссылки с разным регистром, указывающие на один файл.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Не добавлять ссылки с тройной косой чертой или импортированные модули в список скомпилированных файлов.", "Do_not_emit_comments_to_output_6009": "Не создавать комментарии в выходных данных.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Не создавать объявления для кода, имеющего аннотацию \"@internal\".", "Do_not_emit_outputs_6010": "Не создавать выходные данные.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Не выводить выходные элементы, если есть ошибки.", "Do_not_emit_use_strict_directives_in_module_output_6112": "Не порождать директивы use strict в выходных данных модуля.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Не удалять объявления перечислений констант из сгенерированного кода.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Не создавать вспомогательные пользовательские функции, такие как __extends, в скомпилированных выходных данных.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Не включать файл библиотеки по умолчанию (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "Не сообщать об ошибках в недостижимом коде.", "Do_not_report_errors_on_unused_labels_6074": "Не сообщать об ошибках в неиспользуемых метках.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Не разрешать реальный путь symlink.", "Do_not_truncate_error_messages_6165": "Не усекать сообщения об ошибках.", "Duplicate_function_implementation_2393": "Повторяющаяся реализация функции.", "Duplicate_identifier_0_2300": "Повторяющийся идентификатор \"{0}\".", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Повторяющийся идентификатор \"{0}\". Компилятор резервирует имя \"{1}\" в области верхнего уровня модуля.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Повторяющийся идентификатор \"{0}\". Компилятор резервирует имя \"{1}\" в области верхнего уровня для асинхронных функций в модуле.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Дублированный идентификатор \"{0}\". Компилятор резервирует имя \"{1}\" при выпуске ссылок \"super\" в статических инициализаторах.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Повторяющийся идентификатор \"{0}\". Компилятор использует объявление \"{1}\" для поддержки асинхронных функций.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Повторяющийся идентификатор \"{0}\". Статические элементы и элементы экземпляров не могут совместно использовать одно и то же частное имя.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Повторяющийся идентификатор arguments. Компилятор использует arguments для инициализации параметров \"rest\".", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Дублирующийся идентификатор \"_newTarget\". Компилятор использует объявление переменной \"_newTarget\" для получения ссылки на метасвойство \"new.target\".", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Повторяющийся идентификатор \"_this\". Компилятор использует объявление переменной \"_this\" для получения ссылки this.", "Duplicate_index_signature_for_type_0_2374": "Повторяющаяся сигнатура индекса для типа \"{0}\".", "Duplicate_label_0_1114": "Повторяющаяся метка \"{0}\".", "Duplicate_property_0_2718": "Повторяющееся свойство \"{0}\".", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Описатель динамического импорта должен иметь тип \"string\", но имеет тип \"{0}\".", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Динамический импорт поддерживается только в том случае, если для флажка --module установлено значение ''es2020'', ''es2022'', ''esnext'', ''commonjs'', ''amd'', ''system'', ''umd'', ''node16'' или ''nodenext''.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_assertion_as_arguments_1450": "Динамические импорты могут принять спецификатор модуля и необязательное утверждение в качестве аргументов", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_or_nod_1324": "Динамический импорт поддерживает только второй аргумент, если для параметра --module установлено значение ''esnext'', ''node16'' или ''nodenext''.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Каждый член типа объединения \"{0}\" имеет сигнатуры конструкций, но ни одна из их не совместима с другими.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Каждый член типа объединения \"{0}\" имеет сигнатуры, но ни одна из их не совместима с другими.", "Editor_Support_6249": "Поддержка редактора", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "Элемент неявно имеет тип \"any\", так как выражение типа \"{0}\" не может использоваться для индексации типа \"{1}\".", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "Элемент неявно содержит тип any, так как выражение индекса не имеет тип number.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "Элемент неявно имеет тип any, так как тип \"{0}\" не содержит сигнатуру индекса.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "Элемент неявно имеет тип \"any\", так как тип \"{0}\" не содержит сигнатуру индекса. Возможно, вы хотели вызвать \"{1}\"?", "Emit_6246": "Вывести", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Создавать поля класса, соответствующие стандарту ECMAScript.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Порождать метку порядка байтов UTF-8 в начале выходных файлов.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Порождать один файл с сопоставлениями источников, а не создавать отдельный файл.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Вывести профиль ЦП v8 для запуска компилятора для отладки.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Создайте дополнительный файл JavaScript, чтобы упростить поддержку импорта модулей CommonJS. Это включает \"allowSyntheticDefaultImports\" для совместимости типов.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Выведите поля классов с Define вместо Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Выдавать метаданные типа \"Конструктор\" для декорированных объявлений в исходных файлах.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Создать более совместимый, но подробный и менее производительный файл JavaScript для итерации.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Порождать источник вместе с сопоставителями с исходным кодом в одном файле (нужно задать параметр --inlineSourceMap или --sourceMap).", "Enable_all_strict_type_checking_options_6180": "Включить все параметры строгой проверки типов.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Включите цвет и форматирование в выводе TypeScript, чтобы ошибки компилятора легче читались.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Включить ограничения, позволяющие использовать проект TypeScript со ссылками проекта.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Включить отчеты об ошибках для кодовых путей, которые не возвращаются в функции явным образом.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Включите отчеты об ошибках в выражениях и объявлениях с подразумеваемым типом \"any\".", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Включить отчет об ошибках для случаев сбоя в операторах switch.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Включить отчеты об ошибках в файлах JavaScript с проверкой типа.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Включите отчеты об ошибках, если локальные переменные не считываются.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Включите отчеты об ошибках, если параметр \"this\" имеет тип \"any\".", "Enable_experimental_support_for_TC39_stage_2_draft_decorators_6630": "Включить экспериментальную поддержку черновиков оформителей TC39 stage 2.", "Enable_importing_json_files_6689": "Включите импорт файлов JSON.", "Enable_project_compilation_6302": "Включить компиляцию проекта", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Включите строгие методы \"bind\", \"call\" и \"apply\" для функций.", "Enable_strict_checking_of_function_types_6186": "Включение строгой проверки типов функций.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Включение строгой проверки инициализации свойств в классах.", "Enable_strict_null_checks_6113": "Включить строгие проверки NULL.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Включите параметр \"experimentalDecorators\" в файле конфигурации", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Включите флаг \"--jsx\" в файле конфигурации", "Enable_tracing_of_the_name_resolution_process_6085": "Включить трассировку процесса разрешения имен.", "Enable_verbose_logging_6713": "Включите подробное ведение журнала.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Позволяет обеспечивать взаимодействие между модулями CommonJS и ES посредством создания объектов пространства имен для всех импортов. Подразумевает \"allowSyntheticDefaultImports\".", "Enables_experimental_support_for_ES7_decorators_6065": "Включает экспериментальную поддержку для декораторов ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Включает экспериментальную поддержку для создания метаданных типа для декораторов.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Принудительно использует индексированные методы доступа для ключей, объявленных с помощью индексированного типа.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Убедитесь, что переопределяемые элементы в производных классах помечены модификатором переопределения.", "Ensure_that_casing_is_correct_in_imports_6637": "Убедитесь, что при импорте используется правильный регистр.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Убедитесь, что каждый файл можно безопасно перенести, не полагаясь на другой импорт.", "Ensure_use_strict_is_always_emitted_6605": "Убедитесь, что всегда используется \"use strict\".", "Entry_point_for_implicit_type_library_0_1420": "Точка входа для библиотеки неявных типов \"{0}\"", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Точка входа для библиотеки неявных типов \"{0}\" с идентификатором пакета \"{1}\"", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Точка входа для библиотеки типов \"{0}\", указанная в compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Точка входа для библиотеки типов \"{0}\" с идентификатором пакета \"{1}\", указанная в compilerOptions", "Enum_0_used_before_its_declaration_2450": "Перечисление \"{0}\" использовано прежде, чем объявлено.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Объявления перечислений можно объединять только с пространствами имен или другими объявлениями перечислений.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Все объявления перечислений должны иметь значение const или отличное от const.", "Enum_member_expected_1132": "Ожидался элемент перечисления.", "Enum_member_must_have_initializer_1061": "У элемента перечисления должен быть инициализатор.", "Enum_name_cannot_be_0_2431": "Имя перечисления не может иметь значение \"{0}\".", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "Тип перечисления \"{0}\" имеет элементы с инициализаторами, которые не являются литералами.", "Errors_Files_6041": "Файлы ошибок", "Examples_Colon_0_6026": "Примеры: {0}", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Чрезмерная глубина стека при сравнении типов \"{0}\" и \"{1}\".", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Ожидается аргументов типа: {0}–{1}. Укажите их с тегом \"@extends\".", "Expected_0_arguments_but_got_1_2554": "Ожид<PERSON>лось аргументов: {0}, получено: {1}.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "Ожидаемое число аргументов — {0}, фактическое — {1}. Возможно, вы забыли указать void в аргументе типа в Promise?", "Expected_0_type_arguments_but_got_1_2558": "Ожид<PERSON><PERSON><PERSON><PERSON>ь аргументы типа {0}, получены: {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Ожидается аргументов типа: {0}. Укажите их с тегом \"@extends\".", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Ожидался 1 аргу<PERSON><PERSON><PERSON><PERSON>, получено 0. Д<PERSON><PERSON> \"new Promise()\" требуется указание JSDoc, чтобы создать \"resolve\" с возможностью вызова без аргументов.", "Expected_at_least_0_arguments_but_got_1_2555": "Ожид<PERSON>л<PERSON>сь аргументов не меньше: {0}, получено: {1}.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Ожидался соответствующий закрывающий тег JSX для \"{0}\".", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Ожидался соответствующий закрывающий тег фрагмента JSX.", "Expected_for_property_initializer_1442": "Требуется \"=\" для инициализатора свойства.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Ожидаемый тип поля \"{0}\" в \"package.json\" должен быть \"{1}\", получен \"{2}\".", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "Экспериментальная поддержка для декораторов — это функция, которая будет изменена в будущем выпуске. Задайте параметр \"experimentalDecorators\" в \"tsconfig\" или \"jsconfig\", чтобы удалить это предупреждение.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Явно указанный тип разрешения модуля: \"{0}\".", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Невозможно выполнить возведение в степень для значений \"bigint\", если для параметра \"target\" не задана версия \"es2016\" или более поздняя версия.", "Export_0_from_module_1_90059": "Экспорт \"{0}\" из модуля \"{1}\"", "Export_all_referenced_locals_90060": "Экспортировать все локальные объекты, на которые указывают ссылки", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Назначение экспорта невозможно использовать при разработке для модулей ECMAScript. Попробуйте использовать \"export default\" или другой формат модуля.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "Назначение экспорта не поддерживается, если флаг \"--module\" имеет значение \"system\".", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Объявление экспорта конфликтует с экспортированным объявлением \"{0}\".", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Объявления экспорта не разрешены в пространстве имен.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "Описатель экспорта \"{0}\" не существует в области package.json по пути \"{1}\".", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "Экспортированный псевдоним типа \"{0}\" имеет или использует закрытое имя \"{1}\".", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "Экспортированный псевдоним типа \"{0}\" имеет или использует частное имя \"{1}\" из модуля \"{2}\".", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Экспортированная переменная \"{0}\" имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именована.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Экспортированная переменная \"{0}\" имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Экспортированная переменная \"{0}\" имеет или использует закрытое имя \"{1}\".", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Экспорт и назначения экспорта не разрешены в улучшениях модуля.", "Expression_expected_1109": "Ожидалось выражение.", "Expression_or_comma_expected_1137": "Ожидалось выражение или запятая.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "Выражение создает тип кортежа, который слишком большой для представления.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "Выражение создает тип объединения, который слишком сложен для представления.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Разрешение выражения дает идентификатор \"_super\", который используется компилятором для получения ссылки на базовый класс.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "Выражение разрешается в объявление переменной \"_newTarget\", которое компилятор использует для получения ссылки на метасвойство \"new.target\".", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Разрешение выражения дает объявление переменной \"_this\", которое используется компилятором для получения ссылки this.", "Extract_constant_95006": "Извлечь константу", "Extract_function_95005": "Извлечь функцию", "Extract_to_0_in_1_95004": "Извлечь в {0} в {1}", "Extract_to_0_in_1_scope_95008": "Извлечь в {0} в области {1}", "Extract_to_0_in_enclosing_scope_95007": "Извлечь в {0} во включающей области", "Extract_to_interface_95090": "Извлечь в интерфейс", "Extract_to_type_alias_95078": "Извлечь в псевдоним типа", "Extract_to_typedef_95079": "Извлечь в typedef", "Extract_type_95077": "Тип Extract", "FILE_6035": "ФАЙЛ", "FILE_OR_DIRECTORY_6040": "Файл или каталог", "Failed_to_parse_file_0_Colon_1_5014": "Не удалось проанализировать файл \"{0}\": {1}.", "Fallthrough_case_in_switch_7029": "Случай передачи управления в операторе switch.", "File_0_does_not_exist_6096": "Файл \"{0}\" не существует.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Согласно ранее кэшированным поискам, файл \"{0}\" не существует.", "File_0_exist_use_it_as_a_name_resolution_result_6097": "Файл \"{0}\" существует — используйте его как результат разрешения имени.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Согласно ранее кэшированным поискам, файл \"{0}\" существует.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "Файл \"{0}\" имеет неподдерживаемое расширение. Поддерживаются только следующие расширения: {1}.", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "Файл \"{0}\" имеет неподдерживаемое разрешение, поэтому будет пропущен.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "\"{0}\" является файлом JavaScript. Вы хотели включить параметр \"allowJs\"?", "File_0_is_not_a_module_2306": "Файл \"{0}\" не является модулем.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "Файл \"{0}\" отсутствует в списке файлов проекта \"{1}\". Проекты должны перечислять все файлы или использовать шаблон включения.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Файл \"{0}\" отсутствует в \"rootDir\" \"{1}\". Все исходные файлы должны находиться в каталоге \"rootDir\".", "File_0_not_found_6053": "Файл \"{0}\" не найден.", "File_Management_6245": "Управление файлами", "File_change_detected_Starting_incremental_compilation_6032": "Обнаружено изменение в файле. Запускается инкрементная компиляция...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "Файл является модулем CommonJS, так как \"{0}\" не содержит поле \"type\"", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Файл является модулем CommonJS, так как \"{0}\" содержит поле \"type\", значение которого отличается от \"module\"", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Файл является модулем CommonJS, так как \"package.json\" не найден", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Файл является модулем ECMAScript, так как \"{0}\" содержит поле \"type\" со значением \"module\"", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Файл является модулем CommonJS. Его можно преобразовать в модуль ES.", "File_is_default_library_for_target_specified_here_1426": "Файл является библиотекой по умолчанию для указанного здесь целевого объекта.", "File_is_entry_point_of_type_library_specified_here_1419": "Файл является точкой входа для указанной здесь библиотеки типов.", "File_is_included_via_import_here_1399": "Файл включается с помощью импорта.", "File_is_included_via_library_reference_here_1406": "Файл включается с помощью ссылки на библиотеку.", "File_is_included_via_reference_here_1401": "Файл включается с помощью ссылки.", "File_is_included_via_type_library_reference_here_1404": "Файл включается с помощью ссылки на библиотеку типов.", "File_is_library_specified_here_1423": "Файл представляет собой указанную здесь библиотеку.", "File_is_matched_by_files_list_specified_here_1410": "Файл соответствует указанному здесь списку \"files\".", "File_is_matched_by_include_pattern_specified_here_1408": "Файл соответствует указанному здесь шаблону включения.", "File_is_output_from_referenced_project_specified_here_1413": "Файл представляет собой выходные данные для указанного здесь проекта, на который указывает ссылка.", "File_is_output_of_project_reference_source_0_1428": "Файл представляет собой выходные данные для источника ссылки на проект \"{0}\"", "File_is_source_from_referenced_project_specified_here_1416": "Файл представляет собой источник для указанного здесь проекта, на который указывает ссылка.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Файл с именем \"{0}\" отличается от уже включенного файла с именем \"{1}\" только регистром.", "File_name_0_has_a_1_extension_stripping_it_6132": "У имени файла \"{0}\" есть расширение \"{1}\"; расширение удаляется.", "File_redirects_to_file_0_1429": "Файл перенаправляется в файл \"{0}\"", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "Спецификация файла не может содержать родительский каталог (\"..\"), который указывается после рекурсивного подстановочного знака каталога (\"**\"): \"{0}\".", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "Спецификация файла не может заканчиваться рекурсивным подстановочным знаком каталога (\"**\"): \"{0}\".", "Filters_results_from_the_include_option_6627": "Фильтрует результаты в параметре \"включить\".", "Fix_all_detected_spelling_errors_95026": "Исправить все обнаруженные синтаксические ошибки", "Fix_all_expressions_possibly_missing_await_95085": "Исправить все выражения, где может отсутствовать \"await\"", "Fix_all_implicit_this_errors_95107": "Исправить все ошибки неявного this", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Исправьте все неправильные возвращаемые типы асинхронных функций.", "For_await_loops_cannot_be_used_inside_a_class_static_block_18038": "Циклы \"For await\" нельзя использовать внутри статического блока класса.", "Found_0_errors_6217": "Найдено ошибок: {0}.", "Found_0_errors_Watching_for_file_changes_6194": "Найдено ошибок: {0}. Отслеживаются изменения в файлах.", "Found_0_errors_in_1_files_6261": "Обнаружены ошибки ({0}) в файлах ({1}).", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Обнаружены {0} ошибки в этом же файле, начиная с: {1}", "Found_1_error_6216": "Найдено ошибок: 1.", "Found_1_error_Watching_for_file_changes_6193": "Найдена одна ошибка. Отслеживаются изменения в файлах.", "Found_1_error_in_1_6259": "Обнаружена 1 ошибка в {1}", "Found_package_json_at_0_6099": "Обнаружен package.json в \"{0}\".", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "Объявления функций не разрешены в блоках в строгом режиме при нацеливании ES3 или ES5.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "Объявления функций не разрешены в блоках в строгом режиме при нацеливании ES3 или ES5. Определения класса автоматически появляются в строгом режиме.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "Объявления функций не разрешены в блоках в строгом режиме при нацеливании ES3 или ES5. Модули автоматически появляются в строгом режиме.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Выражение функции, у которого нет аннотации типа возвращаемого значения, неявно имеет тип возвращаемого значения \"{0}\".", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Реализация функции отсутствует либо не идет сразу после объявления.", "Function_implementation_name_must_be_0_2389": "Имя реализации функции должно иметь значение \"{0}\".", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Функция неявно имеет тип возвращаемого значения any, так как у нее нет заметки с типом возвращаемого значения, а также на нее прямо или косвенно указывает ссылка в одном из ее выражений \"return\".", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "В функции отсутствует завершающий оператор return, а тип возвращаемого значения не включает \"undefined\".", "Function_not_implemented_95159": "Функция не реализована.", "Function_overload_must_be_static_2387": "Перегрузка функции должна быть статической.", "Function_overload_must_not_be_static_2388": "Перегрузка функции не должна быть статической.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "При использовании в типе объединения нотация типа функции должна быть заключена в круглые скобки.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "При использовании в типе пересечения нотация типа функции должна быть заключена в круглые скобки.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Тип функции, у которого нет заметки с типом возвращаемого значения, неявно имеет тип возвращаемого значения \"{0}\".", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "Функция с телом может объединяться только с классами, которые являются внешними.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Создание D.ts-файлов из файлов TypeScript и JavaScript в проекте.", "Generate_get_and_set_accessors_95046": "Создать методы доступа get и set", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Создать методы доступа get и set для всех переопределяемых свойств", "Generates_a_CPU_profile_6223": "Создает профиль ЦП.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Создает сопоставитель с исходным кодом для каждого соответствующего файла \".d.ts\".", "Generates_an_event_trace_and_a_list_of_types_6237": "Создает трассировку событий и список типов.", "Generates_corresponding_d_ts_file_6002": "Создает соответствующий D.TS-файл.", "Generates_corresponding_map_file_6043": "Создает соответствующий файл с расширением \".map\".", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "Генератор неявно имеет тип yield \"{0}\", так как он не предоставляет никаких значений. Рекомендуется указать заметку с типом возвращаемого значения.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Генераторы не разрешается использовать в окружающем контексте.", "Generic_type_0_requires_1_type_argument_s_2314": "Универсальный тип \"{0}\" требует следующее число аргументов типа: {1}.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Универсальный тип \"{0}\" требует аргументы типа от {1} до {2}.", "Global_module_exports_may_only_appear_at_top_level_1316": "Глобальные операции экспорта модуля могут появиться только на верхнем уровне.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Глобальные операции экспорта модуля могут появиться только в файлах объявления.", "Global_module_exports_may_only_appear_in_module_files_1314": "Глобальные операции экспорта модуля могут появиться только в файлах модуля.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Глобальный тип \"{0}\" должен быть классом или интерфейсом.", "Global_type_0_must_have_1_type_parameter_s_2317": "Глобальный тип \"{0}\" должен иметь следующее число параметров типа: {1}.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Сделайте так, чтобы повторные компиляции в \"--incremental\" и \"--watch\" предполагали, что изменения в файле будут затрагивать только файлы, напрямую зависящие от него.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Сделайте так, чтобы повторные компиляции в проектах, в которых используются режимы \"incremental\" и \"watch\" предполагали, что изменения в файле будут затрагивать только файлы, напрямую зависящие от него.", "Hexadecimal_digit_expected_1125": "Ожидалась шестнадцатеричная цифра.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Требуется идентификатор. \"{0}\" является зарезервированным словом на верхнем уровне модуля.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Ожидался идентификатор. \"{0}\" является зарезервированным словом в строгом режиме.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Ожидался идентификатор. \"{0}\" является зарезервированным словом в строгом режиме. Определения классов автоматически находятся в строгом режиме.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Ожидался идентификатор. \"{0}\" является зарезервированным словом в строгом режиме. Модули автоматически находятся в строгом режиме.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Ожидается идентификатор. \"{0}\" — это зарезервированное слово, которое не может быть использовано здесь.", "Identifier_expected_1003": "Ожидался идентификатор.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Ожидался идентификатор. Значение \"__esModule\" зарезервировано как экспортируемый маркер при преобразовании модулей ECMAScript.", "Identifier_or_string_literal_expected_1478": "Ожидался идентификатор или строковый литерал.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Если пакет \"{0}\" фактически предоставляет этот модуль, рекомендуется отправить запрос на вытягивание, чтобы изменить \"https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}\"", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Если пакет \"{0}\" действительно предоставляет этот модуль, попробуйте добавить новый файл объявления (. d. TS), содержащий \"declare module\" \"{1}\";`", "Ignore_this_error_message_90019": "Пропустите это сообщение об ошибке", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Пропускает tsconfig.json, компилирует указанные файлы с параметрами компилятора по умолчанию", "Implement_all_inherited_abstract_classes_95040": "Реализовать все унаследованные абстрактные классы", "Implement_all_unimplemented_interfaces_95032": "Реализовать все нереализованные интерфейсы", "Implement_inherited_abstract_class_90007": "Реализуйте наследуемый абстрактный класс", "Implement_interface_0_90006": "Реализуйте интерфейс \"{0}\"", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "Предложение Implements экспортированного класса \"{0}\" имеет или использует закрытое имя \"{1}\".", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "Неявное преобразование \"symbol\" в \"string\" приведет к сбою во время выполнения. Рекомендуется заключить это выражение в \"String(...)\".", "Import_0_from_1_90013": "Импорт \"{0}\" из \"{1}\"", "Import_assertion_values_must_be_string_literal_expressions_2837": "Значения утверждения импорта должны быть выражениями строковых литералов.", "Import_assertions_are_not_allowed_on_statements_that_transpile_to_commonjs_require_calls_2836": "Утверждения импорта не разрешены для операторов, которые транскомпилируются в вызовы \"require\" в CommonJS.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_or_nodenext_2821": "Утверждения импорта поддерживаются только в случае, когда для параметра \"--module\" задано значение \"esnext\" или \"nodenext\".", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Утверждения импорта не могут использоваться с импортом или экспортом, затрагивающими только тип.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Назначение импорта невозможно использовать при разработке для модулей ECMAScript. Попробуйте использовать \"import * as ns from \"mod\", \"import {a} from \"mod\", \"import d from \"mod\" или другой формат модуля.", "Import_declaration_0_is_using_private_name_1_4000": "Объявление импорта \"{0}\" использует закрытое имя \"{1}\".", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "Объявление импорта конфликтует с локальным объявлением \"{0}\".", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Объявления импорта в пространстве имен не могут иметь ссылки на модуль.", "Import_emit_helpers_from_tslib_6139": "Импорт вспомогательных объектов, участвующих в порождении, из \"tslib\".", "Import_may_be_converted_to_a_default_import_80003": "Импорт можно преобразовать в импорт по умолчанию.", "Import_name_cannot_be_0_2438": "Имя импорта не может иметь значение \"{0}\".", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Объявление импорта или экспорта во объявлении окружающего модуля не может иметь ссылки на модуль через относительное имя модуля.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "Описатель импорта \"{0}\" не существует в области package.json по пути \"{1}\".", "Imported_via_0_from_file_1_1393": "Импортировано с помощью {0} из файла \"{1}\".", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Импортировано с помощью {0} из файла \"{1}\" для импорта \"importHelpers\", как указано в compilerOptions.", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Импортировано с помощью {0} из файла \"{1}\" для импорта функций фабрики \"jsx\" и \"jsxs\".", "Imported_via_0_from_file_1_with_packageId_2_1394": "Импортировано с помощью {0} из файла \"{1}\" с идентификатором пакета \"{2}\".", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Импортировано с помощью {0} из файла \"{1}\" с идентификатором пакета \"{2}\" для импорта \"importHelpers\", как указано в compilerOptions.", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Импортировано с помощью {0} из файла \"{1}\" с идентификатором пакета \"{2}\" для импорта функций фабрики \"jsx\" и \"jsxs\".", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Операции импорта запрещены в улучшениях модуля. Попробуйте переместить их в содержащий внешний модуль.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Во внешних объявлениях перечислений инициализатор элемента должен быть константным выражением.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "В перечислении с несколькими объявлениями только одно объявление может опустить инициализатор для своего первого элемента перечисления.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Включить список файлов. Не поддерживает шаблоны стандартной маски, в отличие от \"include\".", "Include_modules_imported_with_json_extension_6197": "Включать модули, импортированные с расширением .json", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Включить исходный код в исходные карты в создаваемом файле JavaScript.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Включить файлы исходных карт в создаваемый код JavaScript.", "Includes_imports_of_types_referenced_by_0_90054": "Включает импорт типов, на которые ссылается \"{0}\"", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "При включении --watch, -w начнет отслеживание изменений файла в текущем проекте. После установки можно настроить режим просмотра с помощью:", "Index_signature_for_type_0_is_missing_in_type_1_2329": "В типе \"{1}\" отсутствует сигнатура индекса для типа \"{0}\".", "Index_signature_in_type_0_only_permits_reading_2542": "Сигнатура индекса в типе \"{0}\" разрешает только чтение.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Все отдельные объявления в объединенном объявлении \"{0}\" должны быть экспортированными или локальными.", "Infer_all_types_from_usage_95023": "Вывести все типы исходя из использования", "Infer_function_return_type_95148": "Вывод типа возвращаемого значения функции", "Infer_parameter_types_from_usage_95012": "Выведите типы параметров на основании их использования", "Infer_this_type_of_0_from_usage_95080": "Определить тип \"this\" для \"{0}\" из использования", "Infer_type_of_0_from_usage_95011": "Выведите тип \"{0}\" на основании его использования", "Initialize_property_0_in_the_constructor_90020": "Инициализируйте свойство \"{0}\" в конструкторе", "Initialize_static_property_0_90021": "Инициализируйте статическое свойство \"{0}\"", "Initializer_for_property_0_2811": "Инициализатор для свойства \"{0}\"", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "Инициализатор переменной-элемента экземпляра \"{0}\" не может ссылаться на идентификатор \"{1}\", объявленный в конструкторе.", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "Инициализатор не предоставляет значения для элемента привязки, который не имеет значения по умолчанию.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Инициализаторы не разрешены в окружающих контекстах.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Инициализирует проект TypeScript и создает файл \"tsconfig.json\".", "Insert_command_line_options_and_files_from_a_file_6030": "Вставка параметров командной строки и файлов из файла.", "Install_0_95014": "Установить \"{0}\"", "Install_all_missing_types_packages_95033": "Установить все отсутствующие пакеты типов", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "Интерфейс \"{0}\" не может одновременно расширить типы \"{1}\" и \"{2}\".", "Interface_0_incorrectly_extends_interface_1_2430": "Интерфейс \"{0}\" неправильно расширяет интерфейс \"{1}\".", "Interface_declaration_cannot_have_implements_clause_1176": "Объявление интерфейса не может иметь предложение implements.", "Interface_must_be_given_a_name_1438": "Интерфейсу должно быть присвоено имя.", "Interface_name_cannot_be_0_2427": "Имя интерфейса не может иметь значение \"{0}\".", "Interop_Constraints_6252": "Ограничения взаимодействия", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Интерпретируйте необязательные типы свойств так, как они написаны, а не добавляйте неопределенное значение.", "Invalid_character_1127": "Недопустимый символ.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Недопустимый описатель импорта \"{0}\" не имеет возможных разрешений.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Недопустимое имя модуля в приращении. Модуль \"{0}\" разрешается в модуль без типа в \"{1}\", который невозможно дополнить.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Недопустимое имя модуля в улучшении, не удается найти модуль \"{0}\".", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Недопустимая необязательная цепочка из нового выражения. Вы хотели вызвать ''{0}()''?", "Invalid_reference_directive_syntax_1084": "Недопустимый синтаксис директивы reference.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Недопустимое использование \"{0}\". Его нельзя использовать внутри статического блока класса.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Недопустимое использование \"{0}\". Модули автоматически находятся в строгом режиме.", "Invalid_use_of_0_in_strict_mode_1100": "Недопустимое использование \"{0}\" в строгом режиме.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Недопустимое значение для jsxFactory. \"{0}\" не является допустимым идентификатором или полным именем.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Недопустимое значение \"jsxFragmentFactory\". \"{0}\" не является допустимым идентификатором или полным именем.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Недопустимое значение для \"--reactNamespace\". \"{0}\" не является допустимым идентификатором.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Вероятно, не хватает запятой, разделяющей эти два выражения шаблона. Они формируют выражение шаблона с тегами, которое не может быть вызвано.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Тип элемента \"{0}\" не является допустимым элементом JSX.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Тип экземпляра \"{0}\" не является допустимым элементом JSX.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Тип возвращаемого значения \"{0}\" не является допустимым элементом JSX.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "Параметр \"@{0} {1}\" JSDoc не соответствует предложению \"extends {2}\".", "JSDoc_0_is_not_attached_to_a_class_8022": "Пара<PERSON>етр \"@{0}\" JSDoc не связан с классом.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc \"...\" может использоваться только в последнем параметре сигнатуры.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "У тега \"@param\" JSDoc есть имя \"{0}\", но параметр с таким именем отсутствует.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "Тег \"@param\" JSDoc имеет имя \"{0}\", но параметра с таким именем не существует. Он совпадал бы с \"arguments\", если бы у него был указан тип массива.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "У тега \"@typedef\" JSDoc должна быть аннотация типа, или после него должны стоять теги \"@property\" или \"@member\".", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Типы JSDoc можно использовать только в комментариях в документации.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Типы JSDoc могут быть преобразованы в типы TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Атрибутам JSX должно назначаться только непустое \"expression\".", "JSX_element_0_has_no_corresponding_closing_tag_17008": "Элемент JSX \"{0}\" не содержит соответствующий закрывающий тег.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "Класс элементов JSX не поддерживает атрибуты, так как не имеет свойства \"{0}\".", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "Элемент JSX неявно имеет тип \"any\", так как интерфейс \"JSX.{0}\" не существует.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "Элемент JSX неявно имеет тип any, так как глобальный тип \"JSX.Element\" не существует.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Тип элемента JSX \"{0}\" не имеет конструкций или сигнатур вызова.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Элементы JSX не могут иметь несколько атрибутов с одним именем.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Выражения JSX не могут использовать оператор \"запятая\". Возможно, вы хотели выполнить запись в массив?", "JSX_expressions_must_have_one_parent_element_2657": "Выражения JSX должны иметь один родительский элемент.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Фрагмент JSX не имеет соответствующего закрывающего тега.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Выражения доступа к свойствам JSX не могут содержать имена пространств имен JSX", "JSX_spread_child_must_be_an_array_type_2609": "Дочерний объект расширения JSX должен иметь тип массива.", "JavaScript_Support_6247": "Поддержка JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "Целевой объект перехода не может находиться за границей функции.", "KIND_6034": "ВИД", "Keywords_cannot_contain_escape_characters_1260": "Ключевые слова не могут содержать escape-символы.", "LOCATION_6037": "РАСПОЛОЖЕНИЕ", "Language_and_Environment_6254": "Язык и среда", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Сторона оператора слева от запятой не используется и не имеет побочных эффектов.", "Library_0_specified_in_compilerOptions_1422": "Библиотека \"{0}\", указанная в compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Библиотека, на которую осуществляется ссылка с помощью \"{0}\" из файла \"{1}\"", "Line_break_not_permitted_here_1142": "Здесь запрещено использовать разрыв строки.", "Line_terminator_not_permitted_before_arrow_1200": "Перед стрелкой запрещен символ завершения строки.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Список суффиксов имен файлов для поиска при разрешении модуля.", "List_of_folders_to_include_type_definitions_from_6161": "Список папок, определения типов из которых будут включены.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Список корневых папок, объединенное содержимое которых представляет структуру проекта во время выполнения.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Загружается \"{0}\" из корневого каталога \"{1}\"; расположение кандидата: \"{2}\".", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "Загружается модуль \"{0}\" из папки \"node_modules\", тип целевого файла: \"{1}\".", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "Загружается модуль в виде файла или папки, расположение модуля-кандидата: \"{0}\", тип целевого файла: \"{1}\".", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Языковой стандарт должен иметь форму <язык> или <язык>–<территория>. Например, \"{0}\" или \"{1}\".", "Log_paths_used_during_the_moduleResolution_process_6706": "Пути к журналу, используемые в процессе \"moduleResolution\".", "Longest_matching_prefix_for_0_is_1_6108": "Самый длинный соответствующий префикс для \"{0}\": \"{1}\".", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Поиск в папке node_modules; первоначальное расположение: \"{0}\".", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Сделать все вызовы \"super()\" первой инструкцией в конструкторе", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "Сделать так, чт<PERSON><PERSON><PERSON> keyof возвращал только строки, а не строки, числа или символы. Устаревший вариант.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Сделайте вызов \"super()\" первой инструкцией в конструкторе", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Сопоставленный объект неявно имеет тип шаблона \"любой\".", "Matched_0_condition_1_6403": "Соответствие: \"{0}\", условие: \"{1}\".", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Сопоставление по умолчанию включает шаблон '**/*'", "Matched_by_include_pattern_0_in_1_1407": "Соответствует шаблону включения \"{0}\" в \"{1}\".", "Member_0_implicitly_has_an_1_type_7008": "Элемент \"{0}\" неявно имеет тип \"{1}\".", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "Член \"{0}\" неявно имеет тип \"{1}\", но из использования можно определить более подходящий тип.", "Merge_conflict_marker_encountered_1185": "Встретила<PERSON>ь отметка о конфликте слияния.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "Объединенное объявление \"{0}\" не может включать объявление экспорта по умолчанию. Рекомендуется добавить вместо него отдельное объявление \"export default {0}\".", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "Метасвойство \"{0}\" разрешено только в тексте объявления функции, выражения функции или конструктора.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "Метод \"{0}\" не может иметь реализацию, так как он отмечен в качестве абстрактного.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Метод \"{0}\" экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Метод \"{0}\" экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Method_not_implemented_95158": "Метод не реализован.", "Modifiers_cannot_appear_here_1184": "Здесь невозможно использовать модификаторы.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "Модуль \"{0}\" можно только импортировать по умолчанию с помощью флага \"{1}\"", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Не удается импортировать модуль \"{0}\" с помощью этой конструкции. Описатель разрешается только в модуль ES, который нельзя импортировать с помощью \"require\". Вместо этого используйте импорт ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "Модуль \"{0}\" объявляет \"{1}\" локально, но он экспортируется как \"{2}\".", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "Модуль \"{0}\" объявляет \"{1}\" локально, но он не экспортируется.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "Модуль \"{0}\" не ссылается на тип, но используется здесь как тип. Возможно, вы хотели использовать \"typeof import('{0}')\"?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "Модуль \"{0}\" не ссылается на значение, но используется здесь как значение.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Модуль {0} уже экспортировал элемент с именем \"{1}\". Попробуйте явно повторно экспортировать его, чтобы устранить неоднозначность.", "Module_0_has_no_default_export_1192": "У модуля \"{0}\" нет экспорта по умолчанию.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "В модуле \"{0}\" нет экспорта по умолчанию. Возможно, вы хотели вместо этого использовать \"import { {1} } from {0}\"?", "Module_0_has_no_exported_member_1_2305": "Модуль \"{0}\" не имеет экспортированного элемента \"{1}\".", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "В модуле \"{0}\" нет экспортированного члена \"{1}\". Возможно, вы хотели вместо этого использовать \"import {1} from {0}\"?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "Модуль \"{0}\" скрыт локальным объявлением с таким же именем.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Модуль \"{0}\" использует параметр \"export =\" и не может использоваться с параметром \"export *\".", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "Модуль \"{0}\" был разрешен в окружающий модуль, объявленный в \"{1}\", так как этот файл не был изменен.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Модуль \"{0}\" был разрешен как локально объявленный окружающий модуль в файле \"{1}\".", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "Модуль \"{0}\" был разрешен как \"{1}\", но параметр \"--jsx\" не задан.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "Модуль \"{0}\" был разрешен как \"{1}\", но параметр \"--resolveJsonModule\" не используется.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Имена объявлений модулей могут использовать только строки в кавычках «» или \"\".", "Module_name_0_matched_pattern_1_6092": "Имя модуля \"{0}\", соответствующий шаблон \"{1}\".", "Module_name_0_was_not_resolved_6090": "======== Имя модуля \"{0}\" не было разрешено. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Имя модуля \"{0}\" было успешно разрешено в \"{1}\". ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Имя модуля \"{0}\" было успешно разрешено в \"{1}\" с идентификатором пакета \"{2}\". ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Тип разрешения модуля не указан, используется \"{0}\".", "Module_resolution_using_rootDirs_has_failed_6111": "Произошел сбой при разрешении модуля с помощью \"rootDirs\".", "Modules_6244": "Модули", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Переместить модификаторы элементов маркированного кортежа в метки", "Move_to_a_new_file_95049": "Переместить в новый файл", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Использовать несколько последовательных числовых разделителей запрещено.", "Multiple_constructor_implementations_are_not_allowed_2392": "Не разрешается использование нескольких реализаций конструкторов.", "NEWLINE_6061": "НОВАЯ СТРОКА", "Name_is_not_valid_95136": "Недопустимое имя", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Именованное свойство \"{0}\" содержит типы \"{1}\" и \"{2}\", которые не являются идентичными.", "Namespace_0_has_no_exported_member_1_2694": "Пространство имен \"{0}\" не содержит экспортированный элемент \"{1}\".", "Namespace_must_be_given_a_name_1437": "Пространству имен должно быть задано имя.", "Namespace_name_cannot_be_0_2819": "Имя пространства имен не может быть \"{0}\".", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Ни один конструктор базового класса не имеет указанного числа аргументов типа.", "No_constituent_of_type_0_is_callable_2755": "Нет составляющей типа \"{0}\", которую можно вызвать.", "No_constituent_of_type_0_is_constructable_2759": "Нет составляющей типа \"{0}\", которую можно создать.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "В типе \"{1}\" не обнаружена сигнатура индекса с параметром типа \"{0}\".", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "Не удалось найти входные данные в файле конфигурации \"{0}\". Указанные пути \"include\": \"{1}\", пути \"exclude\": \"{2}\".", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Больше не поддерживается. В ранних версиях кодирование текста устанавливалось вручную для чтения файлов.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "Ни одна перегрузка не ожидает аргументы {0}, но существуют перегрузки, которые ожидают аргументы {1} или {2}.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Ни одна перегрузка не ожидает аргументы типа {0}, но существуют перегрузки, которые ожидают аргументы типа {1} или {2}.", "No_overload_matches_this_call_2769": "Ни одна перегрузка не соответствует этому вызову.", "No_type_could_be_extracted_from_this_type_node_95134": "Не удалось извлечь тип из этого узла типа.", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Не существует значение в области для собирательного свойства \"{0}\". Либо объявите его, либо укажите инициализатор.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "Клас<PERSON> \"{0}\", не являющийся абстрактным, не реализует наследуемый абстрактный элемент \"{1}\" класса \"{2}\".", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Выражение неабстрактного класса не реализует унаследованный абстрактный элемент \"{0}\" класса \"{1}\".", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Утверждения, отличные от NULL, можно использовать только в файлах TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Неотносительные пути не допускаются, если не задано значение параметра baseUrl. Вы забыли указать начальные символы \"./\"?", "Non_simple_parameter_declared_here_1348": "Здесь объявлен не простой параметр.", "Not_all_code_paths_return_a_value_7030": "Не все пути к коду возвращают значение.", "Not_all_constituents_of_type_0_are_callable_2756": "Не все составляющие типа \"{0}\" можно вызвать.", "Not_all_constituents_of_type_0_are_constructable_2760": "Не все составляющие типа \"{0}\" можно создать.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Числовые литералы с абсолютными значениями, равными 2^53 или более, слишком велики для точного представления в виде целых чисел.", "Numeric_separators_are_not_allowed_here_6188": "Числовые разделители здесь запрещены.", "Object_is_of_type_unknown_2571": "Объект имеет тип \"Неизвестный\".", "Object_is_possibly_null_2531": "Возможно, объект равен null.", "Object_is_possibly_null_or_undefined_2533": "Возможно, объект равен null или undefined.", "Object_is_possibly_undefined_2532": "Возможно, объект равен undefined.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Объектный литерал может использовать только известные свойства. \"{0}\" не существует в типе \"{1}\".", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Объектный литерал может указывать только известные свойства, но \"{0}\" не существует в типе \"{1}\". Вы хотели записать \"{2}\"?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "Свойство объектного литерала \"{0}\" неявно имеет тип \"{1}\".", "Octal_digit_expected_1178": "Ожидалась восьмеричная цифра.", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "Литералы восьмеричного типа должны использовать синтаксис ES2015. Используйте синтаксис \"{0}\".", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "Восьмеричные литералы запрещены в инициализаторах членов перечисления. Используйте синтаксис \"{0}\".", "Octal_literals_are_not_allowed_in_strict_mode_1121": "Восьмеричные литералы запрещено использовать в строгом режиме.", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "Восьмеричные литералы недоступны при выборе целевой платформы ECMAScript 5 и более поздних. Используйте синтаксис \"{0}\".", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "В операторе for...in разрешено только одно объявление переменной.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "В операторе for...of разрешено только одно объявление переменной.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "С помощью ключевого слова new можно вызвать только функцию void.", "Only_ambient_modules_can_use_quoted_names_1035": "Имена в кавычках могут использоваться только во внешних модулях.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Только модули amd и system поддерживаются вместе с --{0}.", "Only_emit_d_ts_declaration_files_6014": "Порождаются только файлы объявлений \".d.ts\".", "Only_named_exports_may_use_export_type_1383": "\"export type\" может использоваться только в именованном экспорте.", "Only_numeric_enums_can_have_computed_members_but_this_expression_has_type_0_If_you_do_not_need_exhau_18033": "Только числовые перечисления могут иметь вычисляемые элементы, но это выражение имеет тип \"{0}\". Если вы не хотите проверять полноту, рекомендуется использовать вместо этого объектный литерал.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Вывод только файлов d.ts, но не файлов JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Через ключевое слово super доступны только общие и защищенные методы базового класса.", "Operator_0_cannot_be_applied_to_type_1_2736": "Не удается применить операнд \"{0}\" к типу \"{1}\".", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Оператор \"{0}\" невозможно применить к типам \"{1}\" и \"{2}\".", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Отключить проект от проверки ссылок на несколько проектов при редактировании.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "Параметр \"{0}\" можно указать только в файле \"tsconfig.json\" либо задать значение \"false\" или \"null\" для этого параметра в командной строке.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "Параметр \"{0}\" можно указать только в файле \"tsconfig.json\" либо задать значение \"null\" для этого параметра в командной строке.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "Параметр \"{0}\" можно использовать только при указании \"--inlineSourceMap\" или \"--sourceMap\".", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "Параметр \"{0}\" не может быть указан, если параметр jsx имеет значение \"{1}\".", "Option_0_cannot_be_specified_when_option_target_is_ES3_5048": "Параметр \"{0}\" не может быть указан, если параметр \"target\" имеет значение \"ES3\".", "Option_0_cannot_be_specified_with_option_1_5053": "Параметр \"{0}\" невозможно указать с помощью параметра \"{1}\".", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Параметр \"{0}\" невозможно указать без указания параметра \"{1}\".", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Параметр \"{0}\" нельзя указывать без указания параметра \"{1}\" или \"{2}\".", "Option_build_must_be_the_first_command_line_argument_6369": "Параметр \"--build\" должен быть первым аргументом командной строки.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "Параметр \"--incremental\" можно указать только с помощью tsconfig, выполнив выпуск в отдельный файл или если указан параметр \"--tsBuildInfoFile\".", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "Параметр isolatedModules можно использовать, только если указан параметр --module или если параметр target — ES2015 или выше.", "Option_preserveConstEnums_cannot_be_disabled_when_isolatedModules_is_enabled_5091": "Параметр \"preserveConstEnums\" не может быть отключен, если включен параметр \"isolatedModules\".", "Option_preserveValueImports_can_only_be_used_when_module_is_set_to_es2015_or_later_5095": "Параметр \"preserveValueImports\" можно использовать лишь в случае, если для параметра \"module\" установлено значение \"es2015\" или более позднее.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Параметр project не может быть указан вместе с исходными файлами в командной строке.", "Option_resolveJsonModule_can_only_be_specified_when_module_code_generation_is_commonjs_amd_es2015_or_5071": "Параметр \"--resolveJsonModule\" можно указывать, только когда для создания кода модуля указано значение \"commonjs\", \"amd\", \"es2015\" или \"esNext\".", "Option_resolveJsonModule_cannot_be_specified_without_node_module_resolution_strategy_5070": "Параметр \"--resolveJsonModule\" нельзя указать без стратегии разрешения модуля node.", "Options_0_and_1_cannot_be_combined_6370": "Параметры \"{0}\" и \"{1}\" не могут использоваться одновременно.", "Options_Colon_6027": "Параметры:", "Output_Formatting_6256": "Форматирование выходных данных", "Output_compiler_performance_information_after_building_6615": "Вывод сведений о производительности компиляторов после сборки.", "Output_directory_for_generated_declaration_files_6166": "Выходной каталог для создаваемых файлов объявления.", "Output_file_0_from_project_1_does_not_exist_6309": "Выходной файл \"{0}\" из проекта \"{1}\" не существует", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Выходной файл \"{0}\" не создан из исходного файла \"{1}\".", "Output_from_referenced_project_0_included_because_1_specified_1411": "Выходные данные из проекта \"{0}\", на который указывает ссылка, включены, так как указан \"{1}\".", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Выходные данные из проекта \"{0}\", на который указывает ссылка, включены, так как для параметра \"--module\" указано значение \"none\".", "Output_more_detailed_compiler_performance_information_after_building_6632": "Вывод более подробных сведений о производительности компиляторов после сборки.", "Overload_0_of_1_2_gave_the_following_error_2772": "Перегрузка {0} из {1}, \"{2}\", возвратила следующую ошибку.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Сигнатуры перегрузки должны быть абстрактными или неабстрактными.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Все сигнатуры перегрузки должны быть либо внешними, либо не внешними.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Сигнатуры перегрузки должны быть экспортированы и не экспортированы.", "Overload_signatures_must_all_be_optional_or_required_2386": "Все сигнатуры перегрузки должны быть либо необязательными, либо обязательными.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Все сигнатуры перегрузки должны быть либо общими, либо закрытыми, либо защищенными.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Параметр \"{0}\" не может ссылаться на идентификатор \"{1}\", объявленный после него.", "Parameter_0_cannot_reference_itself_2372": "Параметр \"{0}\" не может ссылаться сам на себя.", "Parameter_0_implicitly_has_an_1_type_7006": "Параметр \"{0}\" неявно имеет тип \"{1}\".", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Параметр \"{0}\" неявно имеет тип \"{1}\", но из использования можно определить более подходящий тип.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Параметр \"{0}\" находится в позиции, отличной от позиции параметра \"{1}\".", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Параметр \"{0}\" метода доступа имеет или использует имя \"{1}\" из внешнего модуля \"{2}\", но не может быть именован.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Параметр \"{0}\" метода доступа имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Параметр \"{0}\" метода доступа имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Параметр \"{0}\" сигнатуры вызова из экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Параметр \"{0}\" сигнатуры вызова из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Параметр \"{0}\" конструктора из экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Параметр \"{0}\" конструктора из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Параметр \"{0}\" конструктора из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Параметр \"{0}\" сигнатуры конструктора из экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Параметр \"{0}\" сигнатуры конструктора из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Параметр \"{0}\" экспортированной функции имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Параметр \"{0}\" экспортированной функции имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Параметр \"{0}\" экспортированной функции имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Параметр \"{0}\" сигнатуры индекса из экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Параметр \"{0}\" сигнатуры индекса из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Параметр \"{0}\" метода из экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Параметр \"{0}\" метода из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Параметр \"{0}\" общего метода из экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Параметр \"{0}\" общего метода из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Параметр \"{0}\" общего метода из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Параметр \"{0}\" общего статического метода из экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Параметр \"{0}\" общего статического метода из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Параметр \"{0}\" общего статического метода из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Parameter_cannot_have_question_mark_and_initializer_1015": "Параметр не может содержать вопросительный знак и инициализатор.", "Parameter_declaration_expected_1138": "Ожидалось объявление параметра.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Параметр имеет имя, но не тип. Возможно, вы хотели использовать \"{0}: {1}\"?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Модификаторы параметров можно использовать только в файлах TypeScript.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Тип параметра открытого метода задания \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Тип параметра открытого метода задания \"{0}\" из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Тип параметра открытого статического метода задания \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Тип параметра открытого статического метода задания \"{0}\" из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Анализ в строгом режиме и создание директивы \"use strict\" для каждого исходного файла.", "Part_of_files_list_in_tsconfig_json_1409": "Часть списка \"files\" в tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "Шаб<PERSON>он \"{0}\" может содержать не больше одного символа \"*\".", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Временные показатели производительности для параметров \"--diagnostics\" и \"--extendedDiagnostics\" недоступны в этом сеансе. Не удалось найти стандартную реализацию API веб-производительности.", "Platform_specific_6912": "Для конкретной платформы", "Prefix_0_with_an_underscore_90025": "Добавьте к \"{0}\" префикс — символ подчеркивания", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Добавить ко всем неправильным объявлениям свойств префикс \"declare\"", "Prefix_all_unused_declarations_with_where_possible_95025": "Добавить префикс \"_\" ко всем неиспользуемым объявлениям, где это возможно", "Prefix_with_declare_95094": "Добавить префикс \"declare\"", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Сохранить неиспользуемые импортированные значения в выходных данных JavaScript, которые в противном случае были бы удалены.", "Print_all_of_the_files_read_during_the_compilation_6653": "Печать всех файлов, считанных во время компиляции.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Печать файлов, считываемых во время компиляции, включая то, почему он был включен.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Печать имен файлов и причины, по которой они включены в компиляцию.", "Print_names_of_files_part_of_the_compilation_6155": "Печатать имена файлов, входящих в компиляцию.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Печать имен файлов, которые являются частью компиляции, а затем остановка обработки.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Печатать имена создаваемых файлов, входящих в компиляцию.", "Print_the_compiler_s_version_6019": "Печать версии компилятора.", "Print_the_final_configuration_instead_of_building_1350": "Печать окончательной конфигурации вместо создания.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Печать имен созданных файлов после компиляции.", "Print_this_message_6017": "Напечатайте это сообщение.", "Private_accessor_was_defined_without_a_getter_2806": "Частный метод доступа был определен без метода получения.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Закрытые идентификаторы запрещено использовать в объявлениях переменных.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Закрытые идентификаторы запрещено использовать вне тела классов.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Закрытые идентификаторы разрешены только в теле класса и могут использоваться только как часть объявления члена класса, доступа к свойствам или левой стороны выражения \"in\"", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Закрытые идентификаторы доступны только при разработке для ECMAScript 2015 или более поздних версий.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Закрытые идентификаторы не могут использоваться в качестве параметров.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "Не удается обратиться к закрытому или защищенному члену \"{0}\" в параметре типа.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Не удается собрать проект \"{0}\", так как его зависимость \"{1}\" содержит ошибки", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Не удается собрать проект \"{0}\", так как его зависимость \"{1}\" не была собрана", "Project_0_is_being_forcibly_rebuilt_6388": "Проект \"{0}\" принудительно перестраивается", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "Проект \"{0}\" устарел, так как файл buildinfo \"{1}показывает, что некоторые изменения не переданы.", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Проект \"{0}\" требует обновления, так как не обновлена его зависимость \"{1}\"", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Проект \"{0}\" устаре<PERSON>, так как выходные данные \"{1}\" старше входных данных \"{2}\".", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Проект \"{0}\" требует обновления, так как выходного файла \"{1}\" не существует", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Проект \"{0}\" устарел, так как выходные данные для него были созданы с помощью версии \"{1}\", которая отличается от текущей версии \"{2}\"", "Project_0_is_out_of_date_because_output_of_its_dependency_1_has_changed_6372": "Проект \"{0}\" устарел, так как изменились выходные данные его зависимости \"{1}\"", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Проект \"{0}\" устарел из-за ошибки при чтении файла \"{1}\"", "Project_0_is_up_to_date_6361": "Проект \"{0}\" не требует обновления", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Проект \"{0}\" ак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, так как новейшие входные данные \"{1}\" старше выходных данных \"{2}\".", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Проект \"{0}\" ак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, но требуется обновить метки времени файлов вывода, которые старше файлов ввода.", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Проект \"{0}\" не требует обновления с файлами .d.ts, взятыми из зависимостей проекта", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Ссылки на проект не могут формировать циклический граф. Обнаружен цикл: {0}", "Projects_6255": "Проекты", "Projects_in_this_build_Colon_0_6355": "Проекты в этой сборке: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Свойства с модификаторо<PERSON> \"accessor\" доступны только при обращении к ECMAScript 2015 и более поздним версиям.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "Свойство \"{0}\" не может содержать инициализатор, так как оно помечено как абстрактное.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "Свойство \"{0}\" поступает из сигнатуры индекса, поэтому доступ к нему должен осуществляться с помощью [\"{0}\"].", "Property_0_does_not_exist_on_type_1_2339": "Свойство \"{0}\" не существует в типе \"{1}\".", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "Свойство \"{0}\" не существует в типе \"{1}\". Вы имели в виду \"{2}\"?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "Свойство \"{0}\" отсутствует в типе \"{1}\". Вы хотели обратиться к статическому элементу \"{2}\"?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "Свойство \"{0}\" не существует в типе \"{1}\". Вы хотите изменить целевую библиотеку? Попробуйте изменить параметр компилятора \"lib\" на \"{2}\" или более поздней версии.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "Свойство \"{0}\" не существует в типе \"{1}\". Попробуйте изменить параметр компилятора \"lib\", включив \"dom\".", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "Свойство \"{0}\" не имеет инициализатора, и ему не гарантировано присваивание в статическом блоке класса.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "Свойство \"{0}\" не имеет инициализатора, и ему не гарантировано присваивание в конструкторе.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "Свойство \"{0}\" неявно имеет тип \"все\", так как для его метода доступа get не задана заметка с типом возвращаемого значения.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "Свойство \"{0}\" неявно имеет тип \"все\", так как для его метода доступа set не задана заметка с типом параметра.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "Свойство \"{0}\" неявно имеет тип \"any\", но из использования можно определить более подходящий тип для его метода доступа get.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "Свойство \"{0}\" неявно имеет тип \"any\", но из использования можно определить более подходящий тип для его метода доступа set.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "Свойство \"{0}\" в типе \"{1}\" невозможно присвоить тому же свойству в базовом типе \"{2}\".", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "Свойство \"{0}\" в типе \"{1}\" не может быть присвоено типу \"{2}\".", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "Свойство \"{0}\" в типе \"{1}\" ссылается на другой член, к которому невозможно обратиться из типа \"{2}\".", "Property_0_is_declared_but_its_value_is_never_read_6138": "Свойство \"{0}\" объявлено, но его значение не было прочитано.", "Property_0_is_incompatible_with_index_signature_2530": "Свойство \"{0}\" несовместимо с сигнатурой индекса.", "Property_0_is_missing_in_type_1_2324": "Свойство \"{0}\" отсутствует в типе \"{1}\".", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "Свойство \"{0}\" отсутствует в типе \"{1}\" и является обязательным в типе \"{2}\".", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "Свойство \"{0}\" недоступно вне класса \"{1}\", так как оно имеет закрытый идентификатор.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "Свойство \"{0}\" является необязательным в типе \"{1}\" и обязательным в типе \"{2}\".", "Property_0_is_private_and_only_accessible_within_class_1_2341": "Свойство \"{0}\" является закрытым и доступно только в классе \"{1}\".", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "Свойство \"{0}\" является закрытым в типе \"{1}\" и не является таковым в типе \"{2}\".", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "Свойство \"{0}\" защищено и доступно только через экземпляр класса \"{1}\". Это экземпляр класса \"{2}\".", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "Свойство \"{0}\" является защищенным и доступно только в классе \"{1}\" и его подклассах.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "Свойство \"{0}\" является защищенным, однако тип \"{1}\" не является классом, производным от \"{2}\".", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "Свойство \"{0}\" является защищенным в типе \"{1}\" и общим в типе \"{2}\".", "Property_0_is_used_before_being_assigned_2565": "Свойство \"{0}\" используется перед присваиванием значения.", "Property_0_is_used_before_its_initialization_2729": "Свойство \"{0}\" используется перед его инициализацией.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "Свойство \"{0}\" может не существовать в типе \"{1}\". Вы имели в виду \"{2}\"?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "Свойство \"{0}\" атрибута расширения JSX не может быть назначено для целевого свойства.", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "Свойство \"{0}\" выражения экспортированного класса не может быть частным или защищенным.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "Свойство \"{0}\" экспортированного интерфейса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "Свойство \"{0}\" экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "Свойство \"{0}\" типа \"{1}\" не может быть назначено типу индекса \"{2}\" \"{3}\".", "Property_0_was_also_declared_here_2733": "Здесь также было объявлено свойство \"{0}\".", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "Свойство \"{0}\" перезапишет базовое свойство в \"{1}\". Если это сделано намеренно, добавьте инициализатор. В противном случае добавьте модификатор \"declare\" или удалите избыточное объявление.", "Property_assignment_expected_1136": "Ожидалось назначение свойства.", "Property_destructuring_pattern_expected_1180": "Ожидался шаблон деструктурирования свойства.", "Property_or_signature_expected_1131": "Ожидалось свойство или сигнатура.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Значение свойства может быть только строковым или числовым литералом, True, False, Null, объектным литералом либо литералом массива.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "Предоставление полной поддержки для итерируемых элементов в for-of, расширение и деструктуризация при выборе целевой платформы ES5 или ES3.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Открытый метод \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Открытый метод \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Открытый метод \"{0}\" экспортированного класса имеет или использует закрытое имя \"{1}\".", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "Общее свойство \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именовано.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "Общее свойство \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "Общее свойство \"{0}\" экспортированного класса имеет или использует закрытое имя \"{1}\".", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Открытый статический метод \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Открытый статический метод \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Открытый статический метод \"{0}\" экспортированного класса имеет или использует закрытое имя \"{1}\".", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Общее статическое свойство \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именовано.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Общее статическое свойство \"{0}\" экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Общее статическое свойство \"{0}\" экспортированного класса имеет или использует закрытое имя \"{1}\".", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Полное имя \"{0}\" запрещено использовать, если перед ним не стоит \"@param {object} {1}\".", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "Возникновение ошибки, если параметр функции не читается.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Вызывать ошибку в выражениях и объявлениях с подразумеваемым типом any.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Вызвать ошибку в выражениях this с неявным типом any.", "Re_exporting_a_type_when_the_isolatedModules_flag_is_provided_requires_using_export_type_1205": "Для повторного экспорта типа при указании флага \"--isolatedModules\" требуется использовать \"export type\".", "Redirect_output_structure_to_the_directory_6006": "Перенаправить структуру вывода в каталог.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Уменьшить количество проектов, автоматически загружаемых с помощью TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "Проект \"{0}\", на который указывает ссылка, не может отключить порождение.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Указанный в ссылке проект \"{0}\" должен иметь следующее значение параметра composite: true.", "Referenced_via_0_from_file_1_1400": "Ссылка с помощью \"{0}\" из файла \"{1}\"", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2834": "Относительные пути импорта требуют явных расширений файлов в импорте EcmaScript, когда ''--moduleResolution'' имеет значение ''node16'' или ''nodenext''. Рассмотрите возможность добавления расширения к пути импорта.", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2835": "Относительные пути импорта требуют явных расширений файлов в импорте EcmaScript, когда ''--moduleResolution'' имеет значение ''node16'' или ''nodenext''. Вы имеете в виду '{0}''?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Удалить список каталогов из процесса просмотра.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Удалить список файлов из обработки в режиме просмотра.", "Remove_all_unnecessary_override_modifiers_95163": "Удалите все ненужные модификаторы \"override\".", "Remove_all_unnecessary_uses_of_await_95087": "Удаление всех ненужных случаев использования \"await\"", "Remove_all_unreachable_code_95051": "Удалить весь недостижимый код", "Remove_all_unused_labels_95054": "Удалить все неиспользуемые метки", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Удалить скобки из всех тел стрелочных функций с соответствующими проблемами", "Remove_braces_from_arrow_function_95060": "Удалить скобки из стрелочной функции", "Remove_braces_from_arrow_function_body_95112": "Удалить скобки из тела стрелочной функции", "Remove_import_from_0_90005": "Удалить импорт из \"{0}\"", "Remove_override_modifier_95161": "Удалите модификатор \"override\".", "Remove_parentheses_95126": "Удалите круглые скобки", "Remove_template_tag_90011": "Удаление тега шаблона", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Снимите ограничение в 20 МБ на общий размер исходного кода для файлов JavaScript на языковом сервере TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Удаление \"type\" из объявления импорта из \"{0}\"", "Remove_type_from_import_of_0_from_1_90056": "Удаление \"type\" из импорта \"{0}\" из \"{1}\"", "Remove_type_parameters_90012": "Удаление параметров типа", "Remove_unnecessary_await_95086": "Удалить ненужный оператор \"await\"", "Remove_unreachable_code_95050": "Удалить недостижимый код", "Remove_unused_declaration_for_Colon_0_90004": "Удаление неиспользуемого объявления для: \"{0}\"", "Remove_unused_declarations_for_Colon_0_90041": "Удалить неиспользуемые объявления для: \"{0}\"", "Remove_unused_destructuring_declaration_90039": "Удалить неиспользуемое объявление деструктурирования", "Remove_unused_label_95053": "Удалить неиспользуемую метку", "Remove_variable_statement_90010": "Удалить оператор с переменной", "Rename_param_tag_name_0_to_1_95173": "Переименовать тег \"@param\" с \"{0}\" на \"{1}\"", "Replace_0_with_Promise_1_90036": "Замените \"{0}\" на \"Promise<{1}>\"", "Replace_all_unused_infer_with_unknown_90031": "Замена всех неиспользуемых \"infer\" на \"unknown\"", "Replace_import_with_0_95015": "Замена импорта на \"{0}\".", "Replace_infer_0_with_unknown_90030": "Замена \"infer {0}\" на \"unknown\"", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Сообщать об ошибке, если не все пути к коду в функции возвращают значение.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Сообщать об ошибках для случаев передачи управления в операторе switch.", "Report_errors_in_js_files_8019": "Сообщать об ошибках в JS-файлах.", "Report_errors_on_unused_locals_6134": "Сообщать об ошибках в неиспользованных локальных переменных.", "Report_errors_on_unused_parameters_6135": "Сообщать об ошибках в неиспользованных параметрах.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Требовать необъявленные свойства из сигнатур индекса для использования доступа к элементам.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Обязательные параметры типа не могут следовать за необязательными параметрами типа.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "Разрешение для модуля \"{0}\" найдено в кэше из расположения \"{1}\".", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "Разрешение для директивы ссылки на тип \"{0}\" обнаружено в кэше из расположения \"{1}\".", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "Разреш<PERSON><PERSON><PERSON> \"keyof\" только в имена свойств со строковым значением (не числа и не символы).", "Resolving_in_0_mode_with_conditions_1_6402": "Разрешение в режиме {0} с условиями {1}.", "Resolving_module_0_from_1_6086": "======== Идет разрешение модуля \"{0}\" из \"{1}\". ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Идет разрешение имени модуля \"{0}\" относительного к базовому URL-адресу \"{1}\" — \"{2}\".", "Resolving_real_path_for_0_result_1_6130": "Разрешается реальный путь для \"{0}\"; результат: \"{1}\".", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Разрешение директивы ссылки на тип \"{0}\", содержащее файл \"{1}\". ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Идет разрешение директивы ссылки на тип \"{0}\", содержащий файл \"{1}\", корневой каталог \"{2}\". ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Идет разрешение директивы ссылки на тип \"{0}\", содержащий файл \"{1}\", корневой каталог не задан. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Идет разрешение директивы ссылки на тип \"{0}\", содержащий файл не задан, корневой каталог \"{1}\". ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Идет разрешение директивы ссылки на тип \"{0}\", содержащий файл не задан, корневой каталог не задан. ========", "Resolving_with_primary_search_path_0_6121": "Разрешается с помощью первичного пути поиска \"{0}\".", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Параметр rest \"{0}\" неявно имеет тип any[].", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Параметр rest \"{0}\" неявно имеет тип \"any[]\", но из использования можно определить более подходящий тип.", "Rest_types_may_only_be_created_from_object_types_2700": "Типы REST можно создавать только из типов объектов.", "Return_type_annotation_circularly_references_itself_2577": "Заметка с типом возвращаемого значения циклически ссылается на саму себя.", "Return_type_must_be_inferred_from_a_function_95149": "Тип возвращаемого значения должен быть определен из функции.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Тип возвращаемого значения сигнатуры вызова из экспортированного интерфейса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Тип возвращаемого значения сигнатуры вызова из экспортированного интерфейса имеет или использует закрытое имя \"{0}\".", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Тип возвращаемого значения сигнатуры конструктора из экспортированного интерфейса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Тип возвращаемого значения сигнатуры конструктора из экспортированного интерфейса имеет или использует закрытое имя \"{0}\".", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Тип возвращаемого значения сигнатуры конструктора должен поддерживать присваивание типу экземпляра класса.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Тип возвращаемого значения экспортированной функции имеет или использует имя \"{0}\" из внешнего модуля {1}, но не может быть именован.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Тип возвращаемого значения экспортированной функции имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Тип возвращаемого значения экспортированной функции имеет или использует закрытое имя \"{0}\".", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Тип возвращаемого значения сигнатуры индекса из экспортированного интерфейса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Тип возвращаемого значения сигнатуры индекса из экспортированного интерфейса имеет или использует закрытое имя \"{0}\".", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Тип возвращаемого значения метода из экспортированного интерфейса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Тип возвращаемого значения метода из экспортированного интерфейса имеет или использует закрытое имя \"{0}\".", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Тип возвращаемого значения открытого метода получения \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Тип возвращаемого значения открытого метода получения \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Тип возвращаемого значения открытого метода получения \"{0}\" из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Тип возвращаемого значения общего метода из экспортированного класса имеет или использует имя \"{0}\" из внешнего модуля {1}, но не может быть именован.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Тип возвращаемого значения общего метода из экспортированного класса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Тип возвращаемого значения общего метода из экспортированного класса имеет или использует закрытое имя \"{0}\".", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Тип возвращаемого значения открытого статического метода получения \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из внешнего модуля {2}, но не может быть именован.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Тип возвращаемого значения открытого статического метода получения \"{0}\" из экспортированного класса имеет или использует имя \"{1}\" из закрытого модуля \"{2}\".", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Тип возвращаемого значения открытого статического метода получения \"{0}\" из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Тип возвращаемого значения общего статического метода из экспортированного класса имеет или использует имя \"{0}\" из внешнего модуля {1}, но не может быть именован.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Тип возвращаемого значения общего статического метода из экспортированного класса имеет или использует имя \"{0}\" из закрытого модуля \"{1}\".", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Тип возвращаемого значения общего статического метода из экспортированного класса имеет или использует закрытое имя \"{0}\".", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Повторное использование модуля \"{0}\" из \"{1}\", найденного в кэше из расположения \"{2}\". Не разрешено.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Повторное использование разрешения модуля \"{0}\" из \"{1}\", найденного в кэше из расположения \"{2}\". Разрешено в \"{3}\".", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Повторное использование разрешения модуля \"{0}\" из \"{1}\", найденного в кэше из расположения \"{2}\". Разрешено в \"{3}\" с ИД пакета \"{4}\".", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Повторное использование разрешения модуля \"{0}\" из \"{1}\" старой программы. Не разрешено.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Повторное использование разрешения модуля \"{0}\" из \"{1}\" старой программы. Разрешено в \"{2}\".", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Повторное использование модуля \"{0}\" из \"{1}\" старой программы. Разрешено в \"{2}\" с ИД пакета \"{3}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\", найденной в кэше из расположения \"{2}\". Не разрешено.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\", найденной в кэше из расположения \"{2}\". Разрешено в \"{3}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\", найденной в кэше из расположения \"{2}\". Разрешено в \"{3}\" с ИД пакета \"{4}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\" старой программы. Не разрешено.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\" старой программы. Разрешено в \"{2}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Повторное использование директивы ссылки на тип \"{0}\" из \"{1}\" старой программы. Разрешено в \"{2}\" с ИД пакета \"{3}\".", "Rewrite_all_as_indexed_access_types_95034": "Перезаписать все как типы с индексным доступом", "Rewrite_as_the_indexed_access_type_0_90026": "Перезапишите как тип с индексным доступом \"{0}\"", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Корневой каталог невозможно определить, идет пропуск первичных путей поиска.", "Root_file_specified_for_compilation_1427": "Корневой файл, указанный для компиляции", "STRATEGY_6039": "СТРАТЕГИЯ", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Сохраните файлы .tsbuildinfo, чтобы обеспечить возможность добавочной компиляции проектов.", "Saw_non_matching_condition_0_6405": "Обнаружено несоответствующее условие \"{0}\".", "Scoped_package_detected_looking_in_0_6182": "Обнаружен пакет, относящийся к области; поиск в \"{0}\"", "Selection_is_not_a_valid_statement_or_statements_95155": "Выделенный фрагмент не является допустимым оператором или операторами.", "Selection_is_not_a_valid_type_node_95133": "Выбранный элемент не является допустимым узлом типа.", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Задать версию языка файла JavaScript для создаваемого файла JavaScript и включения объявлений совместимых библиотек.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Установить язык сообщений из файла TypeScript. Это не влияет на выпуск.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Задание для параметра \"module\" в файле конфигурации значения \"{0}\"", "Set_the_newline_character_for_emitting_files_6659": "Установка символа новой строки для созданных файлов.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Задание для параметра \"target\" в файле конфигурации значения \"{0}\"", "Setters_cannot_return_a_value_2408": "Методы доступа set не могут возвращать значения.", "Show_all_compiler_options_6169": "Отображение всех параметров компилятора.", "Show_diagnostic_information_6149": "Отображение сведений диагностики.", "Show_verbose_diagnostic_information_6150": "Отображение подробных сведений диагностики.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "Показать компоненты, которые будут собраны (или удалены, если дополнительно указан параметр \"--clean\")", "Signature_0_must_be_a_type_predicate_1224": "Сигнатура \"{0}\" должна быть предикатом типа.", "Skip_type_checking_all_d_ts_files_6693": "Пропустить проверку типа всех файлов .d.ts", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Пропуск проверки типа файлов D.ts, включенных в файл TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Пропустить проверку типа файлов объявления.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Сборка проекта \"{0}\" будет пропущена, так как его зависимость \"{1}\" содержит ошибки", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Сборка проекта \"{0}\" будет пропущена, так как его зависимость \"{1}\" не была собрана", "Source_from_referenced_project_0_included_because_1_specified_1414": "Источник из проекта \"{0}\", на который указывает ссылка, включен, так как указан \"{1}\".", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "Источник из проекта \"{0}\", на который указывает ссылка, включен, так как для параметра \"--module\" указано значение \"none\".", "Source_has_0_element_s_but_target_allows_only_1_2619": "Число элементов в источнике — {0}, но целевой объект разрешает только {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "Число элементов в источнике — {0}, но целевой объект требует {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "Источник не предоставляет соответствия для обязательного элемента в позиции {0} в целевом объекте.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "Источник не предоставляет соответствия для элемента с переменным числом аргументов в позиции {0} в целевом объекте.", "Specify_ECMAScript_target_version_6015": "Укажите целевую версию ECMAScript.", "Specify_JSX_code_generation_6080": "Укажите способ создания кода JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Укажите файл, который объединяет все выходные данные в один файл JavaScript. Если параметр \"declaration\" имеет значение true, также обозначает файл, который объединяет весь вывод .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Укажите список шаблонов стандартной маски, соответствующих файлам, которые будут включены в компиляцию.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Укажите список включаемых подключаемых модулей языковой службы.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Укажите набор файлов объявлений связанных библиотек, которые описывают целевую среду выполнения.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Укажите набор записей, которые повторно сопоставляют импорт с дополнительными расположениями поиска.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Укажите массив объектов, которые указывают пути для проектов. Используется в ссылках проекта.", "Specify_an_output_folder_for_all_emitted_files_6678": "Укажите выходную папку для всех выпущенных файлов.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Укажите поведения вывода/проверки для импортов, которые используются только для типов.", "Specify_file_to_store_incremental_compilation_information_6380": "Указание файла для хранения сведений о добавочной компиляции", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Укажите, как TypeScript ищет файл в заданном описателе модуля.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Укажите способ наблюдения за каталогами в системах, в которых отсутствует рекурсивный просмотр файлов.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Укажите, как работает режим отслеживания TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Укажите файлы библиотек для включения в компиляцию.", "Specify_module_code_generation_6016": "Укажите способ создания кода модуля.", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "Укажите стратегию разрешения модуля: node (Node.js) или classic (TypeScript pre-1.6).", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Укажите спецификатор модуля, используемый для импорта функций множителя JSX при использовании \"jsx: react-jsx*\".", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Укажите несколько папок, которые действуют как \"./node_modules/@types\".", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Укажите один или несколько путей или ссылок на модуль узла для файлов базовой конфигурации, от которых наследуются параметры.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Укажите параметры для автоматического получения файлов объявлений.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Укажите стратегию для создания контрольного значения опроса, когда его не удается создать с использованием событий файловой системы: \"FixedInterval\" (по умолчанию), \"PriorityInterval\", \"DynamicPriority\", \"FixedChunkSize\".", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Укажите стратегию для наблюдения за каталогом на платформах, не имеющих собственной поддержки рекурсивного наблюдения: \"UseFsEvents\" (по умолчанию), \"FixedPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\".", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Укажите стратегию для наблюдения за файлом: \"FixedPollingInterval\" (по умолчанию), \"PriorityPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\", \"UseFsEvents\", \"UseFsEventsOnParentDirectory\".", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Укажите ссылку на фрагмент JSX, используемую для фрагментов при нацеливании на вывод React JSX, например \"React.Fragment\" или \"Fragment\".", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Укажите функцию фабрики JSX, используемую при нацеливании на вывод JSX \"react\", например \"React.createElement\" или \"h\".", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Укажите функцию фабрики JSX, используемую при нацеливании на вывод React JSX, например \"React.createElement\" или \"h\".", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Укажите функцию фабрики фрагмента JSX, которая будет использоваться при нацеливании порождения JSX \"react\", если указан параметр компилятора \"jsxFactory\", например \"Fragment\".", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Укажите базовый каталог для разрешения не относительных имен модулей.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Укажите окончание последовательности строки для использования при порождении файлов: CRLF (DOS) или LF (UNIX).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Укажите расположение, в котором отладчик должен найти файлы TypeScript вместо исходных расположений.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Укажите расположение, в котором отладчик должен найти файлы карты, вместо созданных расположений.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Укажите максимальную глубину папки, используемую для проверки файлов JavaScript в \"node_modules\". Применимо только в сочетании с \"allowJs\".", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Укажите описатель модуля, который будет использоваться для импорта функций фабрики \"jsx\" и \"jsxs\", например react", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Укажите объект, вызванный для \"createElement\". Это применимо только при нацеливании на вывод JSX в \"react\".", "Specify_the_output_directory_for_generated_declaration_files_6613": "Укажите выходной каталог для создаваемых файлов объявления.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Укажите путь к файлу добавочной компиляции .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Укажите корневой каталог входных файлов. Используйте его для управления структурой выходных каталогов с --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Укажите корневую папку в исходных файлах.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Укажите корневой путь для отладчиков для поиска исходного кода ссылки.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Укажите имена типов пакетов, которые будут включены без ссылки в исходном файле.", "Specify_what_JSX_code_is_generated_6646": "Укажите, какой код JSX создается.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Укажите, какой подход должен использовать наблюдатель, если наблюдатель за основными файлами вышел из системы.", "Specify_what_module_code_is_generated_6657": "Укажите создаваемый код модуля.", "Split_all_invalid_type_only_imports_1367": "Разделение всех недопустимых импортов, затрагивающих только тип", "Split_into_two_separate_import_declarations_1366": "Разделение на два отдельных объявления импорта", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "Оператор расширения в выражениях new доступен только при разработке для ECMAScript 5 и более поздних версий.", "Spread_types_may_only_be_created_from_object_types_2698": "Типы расширения можно создавать только из типов объектов.", "Starting_compilation_in_watch_mode_6031": "Запуск компиляции в режиме наблюдения...", "Statement_expected_1129": "Ожидался оператор.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Операторы не разрешены в окружающих контекстах.", "Static_members_cannot_reference_class_type_parameters_2302": "Статические элементы не могут ссылаться на параметры типов класса.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "Статическое свойство \"{0}\" конфликтует со встроенным свойством \"Function.{0}\" функции-конструктора \"{1}\".", "String_literal_expected_1141": "Ожидался строковый литерал.", "String_literal_with_double_quotes_expected_1327": "Ожидается строковый литерал с двойными кавычками.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Стилизовать ошибки и сообщения с помощью цвета и контекста (экспериментальная функция).", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Последовательные объявления свойств должны иметь один и тот же тип. Свойство \"{0}\" должно иметь тип \"{1}\", но имеет здесь тип \"{2}\".", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Последующие объявления переменных должны иметь тот же тип. Переменная \"{0}\" должна иметь тип \"{1}\", однако имеет тип \"{2}\".", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Подстановка \"{0}\" для шаблона \"{1}\" содержит неправильный тип, ожидается string, получен \"{2}\".", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "Подстановка \"{0}\" в шаблоне \"{1}\" может содержать не больше одного символа \"*\".", "Substitutions_for_pattern_0_should_be_an_array_5063": "Подстановки для шаблона \"{0}\" должны быть массивом.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Замены для шаблона \"{0}\" не должны быть пустым массивом.", "Successfully_created_a_tsconfig_json_file_6071": "Файл tsconfig.json успешно создан.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Вызовы super не разрешены вне конструкторов или во вложенных функциях внутри конструкторов.", "Suppress_excess_property_checks_for_object_literals_6072": "Подавлять избыточные проверки свойств для объектных литералов.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Подавлять ошибки noImplicitAny для объектов индексирования, у которых отсутствуют сигнатуры индекса.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Подавляйте ошибки \"noImplicitAny\" при индексации объектов без сигнатуры индекса.", "Switch_each_misused_0_to_1_95138": "Изменить все неверно используемые \"{0}\" на \"{1}\"", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Синхронно вызывайте обратные вызовы и обновляйте состояние наблюдателей каталогов на платформах, не имеющих собственной поддержки рекурсивного наблюдения.", "Syntax_Colon_0_6023": "Синтаксис: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "Минимальное ожидаемое число аргументов для тега \"{0}\" — \"{1}\", но фабрика JSX \"{2}\" предоставляет максимум \"{3}\".", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Выражения шаблона с тегами запрещено использовать в необязательной цепочке.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "Целевой объект разрешает только следующее число элементов — {0}, но источник может иметь больше.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "Целевой объект требует следующего числа элементов — {0}, но источник может иметь меньше.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Модифика<PERSON><PERSON><PERSON> \"{0}\" можно использовать только в файлах TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Оператор \"{0}\" невозможно применить к типу Symbol.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "Оператор \"{0}\" не разрешен для логических типов. Попробуйте использовать \"{1}\" вместо него.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "Свойство \"{0}\" асинхронного итератора должно быть методом.", "The_0_property_of_an_iterator_must_be_a_method_2767": "Свойство \"{0}\" итератора должно быть методом.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "Тип Object можно назначить малому количеству других типов. Возможно, вы хотели использовать тип any?", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "Нельзя ссылаться на объект типа arguments в стрелочной функции ES3 и ES5. Используйте стандартное выражение функции.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "На объект arguments невозможно указать ссылку в асинхронной функции или методе в ES3 и ES5. Попробуйте использовать стандартную функцию или метод.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "Текст оператора if не может быть пустым.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "Вызов для этой реализации был выполнен успешно, но сигнатуры реализации перегрузок не видны извне.", "The_character_set_of_the_input_files_6163": "Кодировка входных файлов.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "Содержащая стрелочная функция фиксирует глобальное значение \"this\".", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "Содержащая функция или текст модуля слишком велики для анализа потока управления.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Текущий файл является модулем CommonJS и не может использовать \"await\" на верхнем уровне.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Текущий файл является модулем CommonJS, импорт которого приведет к вызовам \"require\"; однако файл, на который указывает ссылка, является модулем ECMAScript и не может быть импортирован с помощью \"require\". Вместо этого попробуйте написать динамический вызов \"import(\"{0}\")\".", "The_current_host_does_not_support_the_0_option_5001": "Текущий узел не поддерживает параметр \"{0}\".", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "Здесь определяется объявление объекта \"{0}\", который вы, вероятно, намеревались использовать", "The_declaration_was_marked_as_deprecated_here_2798": "Объявление было отмечено как устаревшее.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Ожид<PERSON>емый тип поступает из свойства \"{0}\", объявленного здесь в типе \"{1}\"", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Ожидаемый тип определяется типом возвращаемого значения этой сигнатуры.", "The_expected_type_comes_from_this_index_signature_6501": "Ожидаемый тип определяется этой сигнатурой индекса.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "Выражение назначения экспорта должно представлять собой идентификатор или полное имя в окружающем контексте.", "The_file_is_in_the_program_because_Colon_1430": "Файл включен в программу по следующей причине:", "The_files_list_in_config_file_0_is_empty_18002": "Список \"files\" в файле конфигурации \"{0}\" пуст.", "The_first_export_default_is_here_2752": "Здесь находится первый экспорт данных по умолчанию.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Первым параметром метода then класса promise должен быть обратный вызов.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Глобальный тип \"JSX.{0}\" не может иметь больше одного свойства.", "The_implementation_signature_is_declared_here_2750": "Здесь объявлена сигнатура реализации.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "Метасвойство \"import.meta\" не разрешено в файлах, которые будут построены в выходных данных CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "Мета-свойство import.meta разрешено только в том случае, если параметр --module имеет значение ''es2020'', ''es2022'', ''esnext'', ''system'', ''node16'' или ''nodenext''.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Выводимому типу \"{0}\" невозможно присвоить имя без ссылки на \"{1}\". Вероятно, оно не является переносимым. Требуется заметка с типом.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Выводимый тип \"{0}\" ссылается на тип с циклической структурой, которая не может быть элементарно сериализована. Требуется заметка с типом.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Выведенный тип \"{0}\" ссылается на недоступный тип \"{1}\". Требуется аннотация типа.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Выведенный тип этого узла превышает максимальную длину, которую будет сериализовывать компилятор. Требуется указать заметку явного типа.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "Пересечение \"{0}\" было сокращено до \"never\", так как свойство \"{1}\" существует в нескольких составляющих и является частным в некоторых из них.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "Пересечение \"{0}\" было сокращено до \"never\", так как свойство \"{1}\" имеет конфликтующие типы в некоторых составляющих.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "Ключевое слово intrinsic можно использовать только для объявления внутренних типов, предоставляемых компилятором.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Чтобы использовать фрагменты JSX с параметром компилятора \"jsxFactory\", необходимо указать параметр компилятора \"jsxFragmentFactory\".", "The_last_overload_gave_the_following_error_2770": "Последняя перегрузка возвратила следующую ошибку.", "The_last_overload_is_declared_here_2771": "Здесь объявлена последняя перегрузка.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "Левый операнд оператора for...in не может быть шаблоном деструктурирования.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "Левый операнд оператора for...in не может использовать аннотацию типа.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "Левая часть оператора \"for...in\" не может быть обращением к необязательному свойству.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "В левой части оператора \"for...in\" должна быть переменная или доступ к свойству.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "Левый операнд оператора for...in должен иметь тип string или any.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "Левый операнд оператора for...of не может использовать аннотацию типа.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "Левая часть оператора \"for...of\" не может быть обращением к необязательному свойству.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "Левая часть оператора \"for...of\" не может быть \"async\".", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "В левой части оператора \"for...of\" должна быть переменная или доступ к свойству.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "Левый операнд арифметической операции должен иметь тип \"any\", \"number\", \"bigint\" или тип перечисления.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "Левая часть выражения присваивания не может быть обращением к необязательному свойству.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "В левой части выражения назначения должна быть переменная или доступ к свойству.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "Левый операнд выражения instanceof должен иметь тип any, тип объекта или параметр типа.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Языковой стандарт, который используется при отображении сообщений пользователю (например, en-us)", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Максимальная глубина зависимостей для поиска в папке node_modules и загрузки файлов JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "Операнд оператора \"delete\" не может быть закрытым идентификатором.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "Операнд оператора \"delete\" не может быть свойством только для чтения.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "Операнд оператора \"delete\" должен быть ссылкой на свойство.", "The_operand_of_a_delete_operator_must_be_optional_2790": "Операнд оператора \"delete\" должен быть необязательным.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "Операнд оператора инкремента или декремента не может быть обращением к необязательному свойству.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "Операнд оператора инкремента или декремента должен быть переменной или доступом к свойству.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "Анализатор ожидал найти \"{1}\" для соответствия указанному здесь токену \"{0}\".", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "Корень проекта неоднозначен, но требуется для разрешения записи карты экспорта ''{0}'' в файле ''{1}''. Укажите параметр компилятора ''rootDir'' для устранения неоднозначности.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "Корень проекта неоднозначен, но требуется для разрешения записи карты импорта ''{0}'' в файле ''{1}''. Укажите параметр компилятора ''rootDir'' для устранения неоднозначности.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "Невозможно обратиться к свойству \"{0}\" в типе \"{1}\" внутри этого класса, так как он затемнен другим закрытым идентификатором с таким же написанием.", "The_return_type_of_a_get_accessor_must_be_assignable_to_its_set_accessor_type_2380": "Тип возвращаемого значения метода доступа \"get\" должен быть назначен его типу метода доступа \"set\"", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Тип возвращаемого значения функции декоратора параметра должен быть либо void, либо any.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Тип возвращаемого значения функции декоратора свойства должен быть либо void, либо any.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Тип возвращаемого значения асинхронной функции должен быть допустимым обещанием либо не должен содержать вызываемый элемент \"then\".", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Возвращаемое значение асинхронной функции или метода должно иметь глобальный тип Promise<T>. Вы имели в виду \"Promise<{0}>\"?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "Правая часть оператора \"for…in\" должна иметь тип \"any\", тип объекта или быть параметром типа, однако указан тип \"{0}\".", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "Правый операнд арифметической операции должен иметь тип \"any\", \"number\", \"bigint\" или тип перечисления.", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "Правый операнд выражения instanceof должен иметь тип any или тип, который можно назначить типу интерфейса Function.", "The_root_value_of_a_0_file_must_be_an_object_5092": "Корневое значение файла \"{0}\" должно быть объектом.", "The_shadowing_declaration_of_0_is_defined_here_18017": "Здесь определено объявление затемнения \"{0}\"", "The_signature_0_of_1_is_deprecated_6387": "Сигнатура \"{0}\" \"{1}\" устарела.", "The_specified_path_does_not_exist_Colon_0_5058": "Указанный путь не существует: \"{0}\".", "The_tag_was_first_specified_here_8034": "Этот тег был впервые указан здесь.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "Цель выражения присваивания элемента rest объекта не может быть обращением к необязательному свойству.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "Цель остального назначения объектов должна быть обращением к переменной или свойству.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "Контекст this типа \"{0}\" не может быть назначен методу this типа \"{1}\".", "The_this_types_of_each_signature_are_incompatible_2685": "Типы this каждой подписи несовместимы.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "Тип \"{0}\" является \"readonly\" и не может быть назначен изменяемому типу \"{1}\".", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Невозможно использовать модификатор \"type\" в именованном экпорте, когда в инструкции экспорта используется \"export type\".", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Невозможно использовать модификатор \"type\" в именованном импорте, когда в инструкции импорта используется \"import type\".", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "Тип объявления функции должен соответствовать сигнатуре этой функции.", "The_type_of_this_expression_cannot_be_named_without_a_resolution_mode_assertion_which_is_an_unstable_2841": "Тип этого выражения не может быть назван без утверждения \"режим разрешения\", что является нестабильной функцией. Используйте ночной TypeScript, чтобы отключить эту ошибку. Попробуйте обновить с помощью \"npm install -D typescript@next\".", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "Невозможно сериализовать тип этого узла, поскольку его свойство \"{0}\" не может быть сериализовано.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Возвращаемый тип метода \"{0}()\" асинхронного итератора должен быть обещанием для типа со свойством \"value\".", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Тип, возвращаемый методом \"{0}()\" итератора, должен содержать свойство \"value\".", "The_types_of_0_are_incompatible_between_these_types_2200": "Типы \"{0}\" несовместимы между этими типами.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "Типы, возвращаемые \"{0}\", несовместимы между этими типами.", "The_value_0_cannot_be_used_here_18050": "Здесь нельзя использовать значение \"{0}\".", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "Объявление переменной оператора for...in не может содержать инициализатор.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "Объявление переменной оператора for...of не может содержать инициализатор.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "Оператор with не поддерживается. Все символы в блоке with получат тип any.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Свойство \"{0}\" этого тега JSX ожидает один дочерний объект типа \"{1}\", однако было предоставлено несколько дочерних объектов.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Свойство \"{0}\" этого тега JSX ожидает тип \"{1}\", требующий несколько дочерних объектов, однако был предоставлен только один дочерний объект.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Это сравнение кажется непреднамеренным, поскольку типы \"{0}\" и \"{1}\" не перекрываются.", "This_condition_will_always_return_0_2845": "Это условие всегда возвращает \"{0}\".", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Это условие всегда будет возвращать ''{0}'', так как JavaScript сравнивает объекты по ссылке, а не по значению.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Это условие всегда будет возвращать значение true, поскольку функция \"{0}\" всегда определена.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Это условие будет всегда возвращать значение true, поскольку функция всегда определена. Возможно, вы хотите вызвать ее?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Эту функцию конструктора можно преобразовать в объявление класса.", "This_expression_is_not_callable_2349": "Это выражение не является вызываемым.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Это выражение не может быть вызвано, так как оно является методом доступа get. Вы хотели использовать его без \"()\"?", "This_expression_is_not_constructable_2351": "Это выражение не может быть построено.", "This_file_already_has_a_default_export_95130": "Этот файл уже имеет экспорт по умолчанию.", "This_import_is_never_used_as_a_value_and_must_use_import_type_because_importsNotUsedAsValues_is_set__1371": "Этот импорт никогда не используется в качестве значения и должен использовать \"import type\", так как для \"importsNotUsedAsValues\" задано значение \"error\".", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Это объявление дополняется другим объявлением. Попробуйте переместить дополняющее объявление в тот же файл.", "This_may_be_converted_to_an_async_function_80006": "Это можно преобразовать в асинхронную функцию.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Этот элемент не может иметь комментарий JSDoc с тегом \"@override\", так как он не объявлен в базовом классе \"{0}\".", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Этот элемент не может иметь комментарий JSDoc с тегом \"override\", так как он не объявлен в базовом классе \"{0}\". Возможно, вы имели в виду \"{1}\"?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Этот элемент не может иметь комментарий JSDoc с тегом модификатор \"@override'\", поскольку содержащий его класс \"{0}\" не расширяет другой класс.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Этот элемент не может иметь модификатор \"override\", так как он не объявлен в базовом классе \"{0}\".", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Этот элемент не может иметь модификатор \"override\", так как он не объявлен в базовом классе \"{0}\". Возможно, вы имели в виду \"{1}\"?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Этот элемент не может иметь модификатор \"override\", поскольку содержащий его класс \"{0}\" не расширяет другой класс.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Этот элемент должен иметь комментарий JSDoc с тегом \"@override\", так как он переопределяет элемент в базовом классе \"{0}\".", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Этот элемент должен иметь модификатор \"override\", так как он переопределяет элемент в базовом классе \"{0}\".", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Этот элемент должен иметь модификатор \"override\", так как он переопределяет абстрактный метод, объявленный в базовом классе \"{0}\".", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "На этот модуль можно ссылаться только с помощью импортов/экспортов ECMAScript, включив флаг \"{0}\" и сославшись на его экспорт по умолчанию.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Этот модуль объявлен с помощью оператора \"export =\" и может использоваться только с импортом по умолчанию при использовании флажка \"{0}\".", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Сигнатура перегрузки несовместима с ее сигнатурой реализации.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Этот параметр запрещено использовать с директивой \"use strict\".", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Это свойство параметра должно иметь комментарий JSDoc с тегом \"@override\", так как он переопределяет элемент в базовом классе \"{0}\".", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Это свойство параметра должно иметь модификатор \"override\", так как он переопределяет элемент базового класса \"{0}\".", "This_spread_always_overwrites_this_property_2785": "Это распространение всегда перезаписывает данное свойство.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Этот синтаксис зарезервирован в файлах с расширениями MTS или CTS. Добавьте конечную запятую или явное ограничение.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Этот синтаксис зарезервирован в файлах с расширениями MTS или CTS. Вместо этого используйте выражение \"AS\".", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Для этого синтаксиса требуется импортированный вспомогательный объект, но найти модуль \"{0}\" не удается.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Для этого синтаксиса требуется импортированный вспомогательный объект с именем \"{1}\", который не существует в \"{0}\". Рекомендуется обновить версию \"{0}\".", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Для этого синтаксиса требуется импортированный вспомогательный объект с именем \"{1}\" и параметрами ({2}), который не совместим с объектом в \"{0}\". Попробуйте обновить версию \"{0}\".", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Для этого параметра типа может потребоваться ограничение \"extends {0}\".", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Недопустимое использование \"import\". Можно записывать вызовы \"import()\", но у них должны быть скобки и не должно быть аргументов типа.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Чтобы преобразовать этот файл в модуль ECMAScript, добавьте поле \"type\": \"module\" в \"{0}\".", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Чтобы преобразовать этот файл в модуль ECMAScript, измените его расширение на \"{0}\" или добавьте поле \"type\": \"module\" в \"{1}\".", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Чтобы преобразовать этот файл в модуль ECMAScript, измените его расширение на \"{0}\" или создайте локальный файл package.json с \"{ \"type\": \"module\" }\".", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Чтобы преобразовать этот файл в модуль ECMAScript, создайте локальный файл package.json с \"{ \"type\": \"module\" }\".", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Выражения ожидания верхнего уровня разрешены только в том случае, если для параметра \"module\" установлено значение \"es2022\", \"esnext\", \"system\", \"node16\" или \"nodenext\", а для параметра \"цель\" установлено значение \"es2017\" или выше.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Объявления верхнего уровня в файлах .d.ts должны начинаться с модификатора \"declare\" или \"export\".", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Выражения ожидания верхнего уровня разрешены, только если для параметра \"module\" установлено значение \"es2022\", \"esnext\", \"system\", \"node16\" или \"nodenext\", а для параметра \"target\" установлено значение \"es2017\" или выше.", "Trailing_comma_not_allowed_1009": "Завершающая запятая запрещена.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Транскомпиляция каждого файла как отдельного модуля (аналогично ts.transpileModule).", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Попробуйте использовать команду \"npm i --save-dev @types/{1}\", если он существует, или добавьте новый файл объявления (.d.ts), содержащий \"declare module '{0}';\".", "Trying_other_entries_in_rootDirs_6110": "Попытка использовать другие записи в \"rootDirs\".", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Выполняется попытка замены \"{0}\", расположение модуля кандидата: \"{1}\".", "Tuple_members_must_all_have_names_or_all_not_have_names_5084": "Имена должны быть заданы для всех членов кортежа или не должны быть заданы ни для одного из членов кортежа.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "Тип кортежа \"{0}\" длиной \"{1}\" не имеет элемент с индексом \"{2}\".", "Tuple_type_arguments_circularly_reference_themselves_4110": "Аргументы типа кортежа циклически ссылаются сами на себя.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "Итерация типа \"{0}\" может осуществляться только с использованием флага \"--downlevelIteration\" или с параметром \"--target\" \"es2015\" или выше.", "Type_0_cannot_be_used_as_an_index_type_2538": "Тип \"{0}\" невозможно использовать как тип индекса.", "Type_0_cannot_be_used_to_index_type_1_2536": "Тип \"{0}\" не может использоваться для индексации типа \"{1}\".", "Type_0_does_not_satisfy_the_constraint_1_2344": "Тип \"{0}\" не удовлетворяет ограничению \"{1}\".", "Type_0_does_not_satisfy_the_expected_type_1_1360": "Тип \"{0}\" не соответствует ожидаемому типу \"{1}\".", "Type_0_has_no_call_signatures_2757": "Тип \"{0}\" не содержит сигнатуры вызова.", "Type_0_has_no_construct_signatures_2761": "Тип \"{0}\" не содержит сигнатуры конструкции.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "Тип \"{0}\" не содержит соответствующую сигнатуру индекса для типа \"{1}\".", "Type_0_has_no_properties_in_common_with_type_1_2559": "У типа \"{0}\" нет общих свойств с типом \"{1}\".", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "Тип \"{0}\" не содержит подписей, к которым применим список аргументов типа.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "В типе \"{0}\" отсутствуют следующие свойства из типа \"{1}\": {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "В типе \"{0}\" отсутствуют следующие свойства из типа \"{1}\": {2} и еще {3}.", "Type_0_is_not_a_constructor_function_type_2507": "Тип \"{0}\" не является типом функции конструктора.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "Тип \"{0}\" не является допустимым типом возвращаемого значения асинхронной функции в ES5/ES3, так как он не ссылается на значение конструктора, совместимое с Promise.", "Type_0_is_not_an_array_type_2461": "Тип \"{0}\" не является типом массива.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Тип \"{0}\" не является типом массива или типом строки.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "{0} не является типом массива или строки или в нем нет метода [Symbol.iterator](), который возвращает итератор.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "{0} не является типом массива или в нем нет метода [Symbol.iterator](), который возвращает итератор.", "Type_0_is_not_assignable_to_type_1_2322": "Тип \"{0}\" не может быть назначен для типа \"{1}\".", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "Тип \"{0}\" невозможно присвоить типу \"{1}\". Вы имели в виду \"{2}\"?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Тип \"{0}\" невозможно присвоить типу \"{1}\". Существует два разных типа с таким именем, но они не связаны.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Тип \"{0}\" не может назначаться типу \"{1}\", как подразумевается заметкой о вариантности.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "Невозможно назначить тип \"{0}\" типу \"{1}\", когда свойство \"exactOptionalPropertyTypes\" имеет значение \"true\". Рассмотрите возможность добавления типа \"undefined\" к типам свойств цели.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "Невозможно назначить тип \"{0}\" типу \"{1}\", когда свойство \"exactOptionalPropertyTypes\" имеет значение \"true\". Рассмотрите возможность добавления типа \"undefined\" к типу цели.", "Type_0_is_not_comparable_to_type_1_2678": "Тип \"{0}\" невозможно сравнить с типом \"{1}\".", "Type_0_is_not_generic_2315": "Тип \"{0}\" не является универсальным.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Тип \"{0}\" может представлять примитивное значение, не разрешенное в качестве правого операнда оператора \"in\".", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Тип \"{0}\" должен иметь метод \"[Symbol.asyncIterator]()\", который возвращает асинхронный итератор.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Тип \"{0}\" должен иметь метод \"[Symbol.iterator]()\", который возвращает итератор.", "Type_0_provides_no_match_for_the_signature_1_2658": "Тип \"{0}\" не предоставляет соответствия для сигнатуры \"{1}\".", "Type_0_recursively_references_itself_as_a_base_type_2310": "Тип \"{0}\" рекурсивно ссылается сам на себя как на базовый тип.", "Type_Checking_6248": "Проверка типа", "Type_alias_0_circularly_references_itself_2456": "Псевдоним типа \"{0}\" циклически ссылается на себя.", "Type_alias_must_be_given_a_name_1439": "Псевдониму типа необходимо присвоить имя.", "Type_alias_name_cannot_be_0_2457": "Псевдоним типа не может иметь имя \"{0}\".", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Псевдонимы типов можно использовать только в файлах TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Аннотация типа не может содержаться в объявлении конструктора.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Заметки с типом можно использовать только в файлах TypeScript.", "Type_argument_expected_1140": "Ожидался аргумент типа.", "Type_argument_list_cannot_be_empty_1099": "Список аргументов типа не может быть пустым.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Аргументы типа можно использовать только в файлах TypeScript.", "Type_arguments_cannot_be_used_here_1342": "Использовать аргументы типа здесь недопустимо.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Аргументы типа для \"{0}\" циклически ссылаются сами на себя.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Выражения утверждения типа можно использовать только в файлах TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Тип в позиции {0} в источнике не совместим с типом в позиции {1} в целевом объекте.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Типы в позициях {0}–{1} в источнике не совместимы с типом в позиции {2} в целевом объекте.", "Type_declaration_files_to_be_included_in_compilation_6124": "Файлы объявления типа, включаемые в компиляцию.", "Type_expected_1110": "Ожидался тип.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Утверждения импорта типа должны иметь ровно один ключ \"resolution-mode\" со значением \"import\" или \"require\".", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "Создание экземпляра типа является слишком глубоким и, возможно, бесконечным.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "На тип есть прямые или непрямые ссылки в обратном вызове выполнения собственного метода then.", "Type_library_referenced_via_0_from_file_1_1402": "Библиотека типов, на которую осуществляется ссылка с помощью \"{0}\" из файла \"{1}\"", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Библиотека типов, на которую осуществляется ссылка с помощью \"{0}\" из файла \"{1}\" с идентификатором пакета \"{2}\"", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Тип операнда \"await\" должен быть допустимым обещанием либо не должен содержать вызываемый элемент \"then\".", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Типом значения вычисляемого свойства является \"{0}\", который не может быть назначен типу \"{1}\".", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Инициализатор переменной-элемента экземпляра \"{0}\" не может ссылаться на идентификатор \"{1}\", объявленный в конструкторе.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Тип элементов итерации для операнда \"yield*\" должен быть допустимым обещанием либо не должен содержать вызываемый элемент \"then\".", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Тип свойства \"{0}\" циклически ссылается на самого себя в сопоставленном типе \"{1}\".", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Тип операнда \"yield\" в асинхронном генераторе должен быть допустимым обещанием либо не должен содержать вызываемый элемент \"then\".", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Тип происходит от этого импорта. Импорт стиля пространства имен не может быть вызван или создан и приведет к сбою во время выполнения. Вместо этого рекомендуется использовать импорт по умолчанию или импортировать сюда \"require\".", "Type_parameter_0_has_a_circular_constraint_2313": "Параметр типа \"{0}\" содержит циклическое ограничение.", "Type_parameter_0_has_a_circular_default_2716": "Параметр типа \"{0}\" по умолчанию является циклическим.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Параметр типа \"{0}\" сигнатуры вызова из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Параметр типа \"{0}\" сигнатуры конструктора из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Параметр типа \"{0}\" экспортированного класса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Параметр типа \"{0}\" экспортированной функции имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Параметр типа \"{0}\" экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Параметр типа \"{0}\" для экспортированного типа сопоставленного объекта имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Параметр типа \"{0}\" экспортированного псевдонима типа имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Параметр типа \"{0}\" метода из экспортированного интерфейса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Параметр типа \"{0}\" общего метода из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Параметр типа \"{0}\" общего статического метода из экспортированного класса имеет или использует закрытое имя \"{1}\".", "Type_parameter_declaration_expected_1139": "Ожидалось объявление параметра типа.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Объявления параметров типа можно использовать только в файлах TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Значения по умолчанию для параметров типа могут ссылаться только на ранее объявленные параметры типа.", "Type_parameter_list_cannot_be_empty_1098": "Список параметров типа не может быть пустым.", "Type_parameter_name_cannot_be_0_2368": "Параметр типа не может иметь имя \"{0}\".", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Параметры типов не могут содержаться в объявлении конструктора.", "Type_predicate_0_is_not_assignable_to_1_1226": "Предикат типов \"{0}\" не может быть назначен для \"{1}\".", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "Тип создает тип кортежа, который слишком большой для представления.", "Type_reference_directive_0_was_not_resolved_6120": "======== Директива ссылки на тип \"{0}\" не разрешена. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== Директива ссылки на тип \"{0}\" успешно разрешена в \"{1}\", первичный объект: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== Директива ссылки на тип \"{0}\" успешно разрешена в \"{1}\" с идентификатором пакета \"{2}\", первичный объект: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Выражения соответствия типа можно использовать только в файлах TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Типы не могут отображаться в объявлениях экспорта в файлах JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Типы имеют раздельные объявления закрытого свойства \"{0}\".", "Types_of_construct_signatures_are_incompatible_2419": "Типы сигнатур конструкций несовместимы.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Типы параметров \"{0}\" и \"{1}\" несовместимы.", "Types_of_property_0_are_incompatible_2326": "Типы свойства \"{0}\" несовместимы.", "Unable_to_open_file_0_6050": "Не удается открыть файл \"{0}\".", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Не удается разрешить сигнатуру декоратора класса при вызове в качестве выражения.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Не удается разрешить сигнатуру декоратора метода при вызове в качестве выражения.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Не удается разрешить сигнатуру декоратора параметра при вызове в качестве выражения.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Не удается разрешить сигнатуру декоратора свойства при вызове в качестве выражения.", "Unexpected_end_of_text_1126": "Неожиданный конец текста.", "Unexpected_keyword_or_identifier_1434": "Непредвиденное ключевое слово или идентификатор.", "Unexpected_token_1012": "Неожиданный токен.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Неожиданный токен. Ожидался конструктор, метод, метод доступа или свойство.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Непредвиденная лексема. Ожидалось имя параметра типа без фигурных скобок.", "Unexpected_token_Did_you_mean_or_gt_1382": "Непредвиденный токен. Возможно, вы хотели использовать \"{'>'}\" или \"&gt;\"?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Непредвиденный токен. Возможно, вы хотели использовать \"{'}'}\" или \"&rbrace;\"?", "Unexpected_token_expected_1179": "Неожиданный токен. Ожидался символ \"{\".", "Unknown_build_option_0_5072": "Неизвестный параметр сборки \"{0}\".", "Unknown_build_option_0_Did_you_mean_1_5077": "Неизвестный параметр сборки \"{0}\". Возможно, вы хотели использовать \"{1}\"?", "Unknown_compiler_option_0_5023": "Неизвестный параметр компилятора \"{0}\".", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Неизвестный параметр компилятора \"{0}\". Возможно, вы хотели использовать \"{1}\"?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Неизвестное ключевое слово или идентификатор. Вы имели в виду \"{0}\"?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Неизвестный параметр excludes. Возможно, вы имели в виду exclude?", "Unknown_type_acquisition_option_0_17010": "Неизвестный параметр получения типа, \"{0}\".", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Неизвестный параметр получения типа \"{0}\". Возможно, вы хотели использовать \"{1}\"?", "Unknown_watch_option_0_5078": "Неизвестный параметр контрольного значения \"{0}\".", "Unknown_watch_option_0_Did_you_mean_1_5079": "Неизвестный параметр контрольного значения \"{0}\". Возможно, вы хотели использовать \"{1}\"?", "Unreachable_code_detected_7027": "Обнаружен недостижимый код.", "Unterminated_Unicode_escape_sequence_1199": "Незавершенная escape-последовательность Юникода.", "Unterminated_quoted_string_in_response_file_0_6045": "Незавершенная строка в кавычках в файле ответов \"{0}\".", "Unterminated_regular_expression_literal_1161": "Незавершенный литерал регулярного выражения.", "Unterminated_string_literal_1002": "Строковый литерал без признака конца.", "Unterminated_template_literal_1160": "Незавершенный литерал шаблона.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Вызовы функций без типов не могут принимать аргументы типов.", "Unused_label_7028": "Неиспользуемая метка.", "Unused_ts_expect_error_directive_2578": "Неиспользуемая директива \"@ts-expect-error\".", "Update_import_from_0_90058": "Обновить импорт из \"{0}\"", "Updating_output_of_project_0_6373": "Обновление выходных данных проекта \"{0}\"...", "Updating_output_timestamps_of_project_0_6359": "Обновление меток времени в выходных данных проекта \"{0}\"...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Обновление меток времени в неизменившихся выходных данных проекта \"{0}\"...", "Use_0_95174": "Использовать \"{0}\".", "Use_Number_isNaN_in_all_conditions_95175": "Использовать \"Number.isNaN\" во всех условиях.", "Use_element_access_for_0_95145": "Использовать доступ к элементам для \"{0}\".", "Use_element_access_for_all_undeclared_properties_95146": "Использовать доступ к элементам для всех необъявленных свойств.", "Use_synthetic_default_member_95016": "Используйте искусственный элемент \"default\".", "Using_0_subpath_1_with_target_2_6404": "Использование \"{0}\", вложенный путь: \"{1}\", целевой объект: \"{2}\".", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Использование строки для оператора for...of поддерживается только в ECMAScript 5 и более поздних версиях.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Использование --build,-b заставит TSC вести себя больше как оркестратор сборки, чем как компилятор. Это используется для запуска создания составных проектов, о которых можно узнать больше на {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Использование параметров компилятора для перенаправления ссылки на проект \"{0}\".", "VERSION_6036": "ВЕРСИЯ", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "Значение типа \"{0}\" не имеет общих свойств со значением типа \"{1}\". Вы хотели вызвать его?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Значение типа \"{0}\" не может вызываться. Вы хотели использовать new?", "Variable_0_implicitly_has_an_1_type_7005": "Переменная \"{0}\" неявно имеет тип \"{1}\".", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "Переменная \"{0}\" неявно имеет тип \"{1}\", но из использования можно определить более подходящий тип.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "Переменная \"{0}\" неявно имеет тип \"{1}\" в некоторых расположениях, но из использования можно определить более подходящий тип.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "Переменная \"{0}\" неявным образом получает тип \"{1}\" в некоторых местах, где ее тип невозможно определить.", "Variable_0_is_used_before_being_assigned_2454": "Переменная \"{0}\" используется перед назначением.", "Variable_declaration_expected_1134": "Ожидалось объявление переменной.", "Variable_declaration_list_cannot_be_empty_1123": "Список объявлений переменной не может быть пустым.", "Variable_declaration_not_allowed_at_this_location_1440": "Объявление переменной в этом расположении запрещено.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "Элемент с переменным числом аргументов в позиции {0} в источнике не соответствует элементу в позиции {1} в целевом объекте.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Заметки вариантности поддерживаются только в псевдонимах типов для объектов, функций, конструкторов и сопоставленных типов.", "Version_0_6029": "Вер<PERSON>ия {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Дополнительные сведения об этом файле: https://aka.ms/tsconfig.", "WATCH_OPTIONS_6918": "ПАРАМЕТРЫ ПРОСМОТРА", "Watch_and_Build_Modes_6250": "Режимы отслеживания и сборки", "Watch_input_files_6005": "Просмотр входных файлов.", "Watch_option_0_requires_a_value_of_type_1_5080": "Параметр \"{0}\" контрольного значения требует значение типа {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Записать тип для \"{0}\" можно, только добавив здесь тип для всего параметра.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "При назначении функций убедитесь, что параметры и возвращаемые значения совместимы с подтипом.", "When_type_checking_take_into_account_null_and_undefined_6699": "При проверке типа учитывайте параметры \"null\" и \"undefined\".", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Сохранять ли устаревшие выходные данные консоли в режиме просмотра вместо очистки экрана.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Заключение всех недопустимых символов в контейнер выражений", "Wrap_all_object_literal_with_parentheses_95116": "Заключить все литералы объектов в круглые скобки", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Перенос всех элементов JSX без родительских элементов во фрагмент JSX", "Wrap_in_JSX_fragment_95120": "Перенос во фрагмент JSX", "Wrap_invalid_character_in_an_expression_container_95108": "Заключение недопустимого знака в контейнер выражений", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Заключить следующий текст в круглые скобки, которые должны быть литералом объекта", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Вы можете узнать обо всех параметрах компилятора на {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Вы не можете переименовать модуль с помощью глобального импорта.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Невозможно переименовать элементы, определенные в папке \"node_modules\".", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Невозможно переименовать элементы, определенные в другой папке \"node_modules\".", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Невозможно переименовать элементы, определенные в стандартной библиотеке TypeScript.", "You_cannot_rename_this_element_8000": "Этот элемент переименовать нельзя.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "\"{0}\" принимает слишком мало аргументов для использования в качестве декоратора. Вы хотели сначала вызвать его и записать \"@{0}()\"?", "_0_and_1_index_signatures_are_incompatible_2330": "Сигнатуры индекса \"{0}\" и \"{1}\" несовместимы.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Операции \"{0}\" и \"{1}\" невозможно использовать одновременно без скобок.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "\"{0}\" указаны дважды. Атрибут \"{0}\" будет перезаписан.", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "Для импорта \"{0}\" необходимо установить флаг \"esModuleInterop\" и использовать импорт по умолчанию.", "_0_can_only_be_imported_by_using_a_default_import_2595": "Для импорта \"{0}\" необходимо использовать импорт по умолчанию.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "Для импорта \"{0}\" необходимо использовать вызов \"require\" или установить флаг \"esModuleInterop\" и использовать импорт по умолчанию.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "Для импорта \"{0}\" необходимо использовать вызов \"require\" или импорт по умолчанию.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "Для импорта \"{0}\" необходимо использовать \"import {1} = require({2})\" или импорт по умолчанию.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "Для импорта \"{0}\" необходимо использовать \"import {1} = require({2})\" или установить флаг \"esModuleInterop\" и использовать импорт по умолчанию.", "_0_cannot_be_compiled_under_isolatedModules_because_it_is_considered_a_global_script_file_Add_an_imp_1208": "\"{0}\" не может быть скомпилирован с параметром \"--isolatedModules\", так как он считается глобальным файлом сценария. Добавьте оператор для импорта, экспорта или пустой оператор \"export {}\", чтобы сделать его модулем.", "_0_cannot_be_used_as_a_JSX_component_2786": "\"{0}\" невозможно использовать как компонент JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "\"{0}\" невозможно использовать как значение, так как он был экспортирован с помощью \"export type\".", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "\"{0}\" невозможно использовать как значение, так как он был импортирован с помощью \"import type\".", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Компоненты \"{0}\" не принимают текст в виде дочерних элементов. Текст в JSX-файле имеет тип \"string\", однако для \"{1}\" ожидается тип \"{2}\".", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "Возможно создание экземпляра \"{0}\" с произвольным типом, который может быть не связан с \"{1}\".", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "Объявления \"{0}\" можно использовать только в файлах TypeScript.", "_0_expected_1005": "Ожидалось \"{0}\".", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "В \"{0}\" нет экспортированного элемента \"{1}\". Вы имели в виду \"{2}\"?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "\"{0}\" неявно имеет тип возвращаемого значения \"{1}\", но из использования можно определить более подходящий тип.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "\"{0}\" неявно имеет тип возвращаемого значения any, так как у него нет заметки с типом возвращаемого значения, а также на него прямо или косвенно указана ссылка в одном из его выражений return.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "\"{0}\" неявно имеет тип any, так как отсутствует аннотация типа и на \"{0}\" есть прямые или непрямые ссылки в его собственном инициализаторе.", "_0_index_signatures_are_incompatible_2634": "Сигнатуры индекса \"{0}\" несовместимы.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "Тип индекса \"{0}\" \"{1}\" не может быть назначен типу индекса \"{2}\" \"{3}\".", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "\"{0}\" является примитивом, но \"{1}\" — объект оболочки. Предпочтительно использовать \"{0}\" по мере возможности.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "\"{0}\" является типом и не может импортироваться в файлы JavaScript. Используйте \"{1}\" в заметке типа JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_preserveValueImports_and_isolatedMod_1444": "\"{0}\" является типом. Чтобы импортировать его, необходимо использовать импорт, распространяющийся только на тип, если включены параметры \"preserveValueImports\" и \"isolatedModules\".", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "\"{0}\" является неиспользуемым переименованием \"{1}\". Возможно, это должна была быть заметка для типа?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "\"{0}\" может быть назначен ограничению типа \"{1}\", но можно создать экземпляр \"{1}\" с другим подтипом ограничения \"{2}\".", "_0_is_automatically_exported_here_18044": "\"{0}\" экспортирован автоматически.", "_0_is_declared_but_its_value_is_never_read_6133": "Свойство \"{0}\" объявлено, но его значение не было прочитано.", "_0_is_declared_but_never_used_6196": "\"{0}\" объя<PERSON><PERSON>е<PERSON>, но никогда не использовался.", "_0_is_declared_here_2728": "Здесь объявлен \"{0}\".", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "\"{0}\" определен как свойство в классе \"{1}\", но переопределяется здесь в \"{2}\" как метод доступа.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "\"{0}\" определен как метод доступа в классе \"{1}\", но переопределяется здесь в \"{2}\" как свойство экземпляра.", "_0_is_deprecated_6385": "\"{0}\" устарело.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "\"{0}\" не является допустимым метасвойством для ключевого слова \"{1}\". Вы имели в виду \"{2}\"?", "_0_is_not_allowed_as_a_parameter_name_1390": "\"{0}\" не является допустимым именем параметра.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "Использование \"{0}\" в качестве имени объявления переменной не допускается.", "_0_is_of_type_unknown_18046": "\"{0}\" относится к типу unknown.", "_0_is_possibly_null_18047": "Возможно, \"{0}\" имеет значение null.", "_0_is_possibly_null_or_undefined_18049": "Возможно, \"{0}\" имеет значение null или undefined.", "_0_is_possibly_undefined_18048": "Возможно, \"{0}\" имеет значение undefined.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "На \"{0}\" есть прямые или непрямые ссылки в его собственном базовом выражении.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "На \"{0}\" есть прямые или непрямые ссылки в его собственной аннотации типа.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "\"{0}\" указан несколько раз, поэтому такое использование будет перезаписано.", "_0_list_cannot_be_empty_1097": "Список \"{0}\" не может быть пустым.", "_0_modifier_already_seen_1030": "Модифика<PERSON>ор \"{0}\" уже встречался.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "Модифика<PERSON>ор \"{0}\" может присутствовать только в параметре типа у класса, интерфейса или псевдонима типа.", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "Модифика<PERSON>ор \"{0}\" не может содержаться в объявлении конструктора.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "Модифика<PERSON><PERSON>р \"{0}\" не может отображаться в модуле или элементе пространства имен.", "_0_modifier_cannot_appear_on_a_parameter_1090": "Модифика<PERSON>ор \"{0}\" не может содержаться в параметре.", "_0_modifier_cannot_appear_on_a_type_member_1070": "Модифик<PERSON><PERSON><PERSON><PERSON> \"{0}\" не может отображаться в элементе типа.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "Модифик<PERSON><PERSON><PERSON><PERSON> \"{0}\" не может присутствовать в параметре типа.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "Модифика<PERSON><PERSON><PERSON> \"{0}\" не может отображаться в сигнатуре индекса.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "Модифика<PERSON>ор \"{0}\" не может использоваться для элементов класса этого типа.", "_0_modifier_cannot_be_used_here_1042": "Модифика<PERSON>ор \"{0}\" не может использоваться здесь.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "Модифика<PERSON>ор \"{0}\" не может использоваться в окружающем контексте.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "Модификатор \"{0}\" не может использоваться с модификатором \"{1}\".", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "Модификатор \"{0}\" не может использоваться с закрытым идентификатором.", "_0_modifier_must_precede_1_modifier_1029": "Модификатор \"{0}\" должен предшествовать модификатору \"{1}\".", "_0_needs_an_explicit_type_annotation_2782": "Для \"{0}\" требуется явная заметка с типом.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "\"{0}\" относится только к типу, а здесь используется как пространство имен.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "\"{0}\" относится только к типу, но используется здесь как значение.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "\"{0}\" относится только к типу, но используется здесь как значение. Вы хотели использовать \"{1} в {0}\"?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "\"{0}\" относится только к типу, но здесь используется как значение. Вы хотите изменить целевую библиотеку? Попробуйте изменить параметр компилятора \"lib\" на ES2015 или более поздней версии.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "\"{0}\" ссылается на глобальную переменную UMD, но текущий файл является модулем. Рекомендуется добавить импорт.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "\"{0}\" относится к значению, но здесь используется как тип. Возможно, вы имели в виду \"typeof {0}\"?", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_preserveVa_1446": "\"{0}\" разрешается в объявление, распространяющееся только на тип. Чтобы импортировать его, необходимо использовать импорт, распространяющийся только на тип, если включены параметры \"preserveValueImports\" и \"isolatedModules\".", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_isol_1448": "\"{0}\" разрешается в объявление, распространяющееся только на тип. Чтобы повторно его экспортировать, необходимо использовать повторный экспорт, распространяющийся только на тип, если включен параметр \"isolatedModules\".", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "Значение \"{0}\" должно быть задано внутри объекта \"compilerOptions\" файла конфигурации JSON", "_0_tag_already_specified_1223": "Тег \"{0}\" уже указан.", "_0_was_also_declared_here_6203": "Здесь также был объявлен \"{0}\".", "_0_was_exported_here_1377": "Здесь был экспортирован \"{0}\".", "_0_was_imported_here_1376": "Здесь был импортирован \"{0}\".", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "\"{0}\", у которого нет аннотации типа возвращаемого значения, неявно имеет тип возвращаемого значения \"{1}\".", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "\"{0}\", у которого нет заметки с типом возвращаемого значения, неявно имеет тип yield \"{1}\".", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Модификатор abstract может отображаться только в объявлении класса, метода или свойства.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Модифи<PERSON><PERSON><PERSON><PERSON><PERSON> \"accessor\" может быть только в объявлении свойства.", "and_here_6204": "и здесь.", "arguments_cannot_be_referenced_in_property_initializers_2815": "Ссылка на \"arguments\" в инициализаторах свойств невозможна.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": обрабатывать файлы с импортом, экспортом, import.meta, jsx (с jsx: react-jsx) или форматом esm (с модулем: node16+) как файлы с модулями.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Выражения \"await\" допускаются только на верхнем уровне файла, если он является модулем, но не имеет импортов и экспортов. Рекомендуется добавить пустой элемент \"export {}\", чтобы сделать этот файл модулем.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Выражения \"await\" допускаются только в асинхронных функциях и на верхних уровнях модулей.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Выражения await не могут быть использованы в инициализаторе параметра.", "await_has_no_effect_on_the_type_of_this_expression_80007": "\"await\" не влияет на тип этого выражения.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "Параметр \"baseUrl\" имеет значение \"{0}\", это значение используется для разрешения безотносительного имени модуля \"{1}\".", "can_only_be_used_at_the_start_of_a_file_18026": "\"#!\" можно использовать только в начале файла.", "case_or_default_expected_1130": "Ожидалось case или default.", "catch_or_finally_expected_1472": "Ожида<PERSON>т<PERSON>я \"catch\" или \"finally\".", "const_declarations_can_only_be_declared_inside_a_block_1156": "Объявления const можно задать только в блоке.", "const_declarations_must_be_initialized_1155": "Объявления \"const\" должны быть инициализированы.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "Инициализатор элементов перечисления const был вычислен в неконечное значение.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "Инициализатор элементов перечисления const был вычислен в запрещенное значение NaN.", "const_enum_member_initializers_can_only_contain_literal_values_and_other_computed_enum_values_2474": "Инициализаторы членов перечисления констант могут содержать только литеральные значения и другие вычисляемые значения перечисления.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Перечисления const можно использовать только в выражениях доступа к свойству или индексу, а также в правой части объявления импорта, назначения экспорта или запроса типа.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "Недопустимо использовать constructor как имя свойства параметра.", "constructor_is_a_reserved_word_18012": "\"#constructor\" является зарезервированным словом.", "default_Colon_6903": "по умолчанию:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "Невозможно вызвать оператор delete с идентификатором в строгом режиме.", "export_Asterisk_does_not_re_export_a_default_1195": "При использовании \"export *\" повторный экспорт по умолчанию не выполняется.", "export_can_only_be_used_in_TypeScript_files_8003": "Элемент \"export =\" можно использовать только в файлах TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Модификатор export невозможно применить к неоднозначным модулям и улучшениям модулей, так как они всегда видимые.", "extends_clause_already_seen_1172": "Предложение extends уже существует.", "extends_clause_must_precede_implements_clause_1173": "Предложение extends должно предшествовать предложению implements.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "Предложение extends экспортированного класса \"{0}\" имеет или использует закрытое имя \"{1}\".", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "Предложение extends экспортированного класса имеет или использует закрытое имя \"{0}\".", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "Предложение extends экспортированного интерфейса \"{0}\" имеет или использует закрытое имя \"{1}\".", "false_unless_composite_is_set_6906": "\"false\", если не задано значение \"composite\"", "false_unless_strict_is_set_6905": "\"false\", если не задано \"strict\"", "file_6025": "файл", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "Циклы \"for await\" допускаются только на верхнем уровне файла, если он является модулем, но этот файл не содержит импортов или экспортов. Рекомендуется добавить пустой элемент \"export {}\", чтобы сделать этот файл модулем.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "Циклы \"for await\" допускаются только в асинхронных функциях и на верхних уровнях модулей.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "Методы доступа \"get\" и \"set\" не могут объявлять параметры \"this\".", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "\"[]\", если указаны \"files\", в противном случае \"[\"**/*\"]5D;\"", "implements_clause_already_seen_1175": "Предложение implements уже существует.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Предложения \"implements\" можно использовать только в файлах TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "Элемент \"import ... =\" можно использовать только в файлах TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Объявления \"infer\" допустимы только в предложении \"extends\" условного типа.", "let_declarations_can_only_be_declared_inside_a_block_1157": "Объявления let можно задать только в блоке.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "Не допускается использование let в качестве имени в объявлениях let или const.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "модуль === \"AMD\" или \"UMD\" или \"Система\" или \"ES6\", затем \"Классический\", или \"Узел\"", "module_system_or_esModuleInterop_6904": "модуль === \"system\" или \"esModuleInterop\"", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "Выражение new, у цели которого нет сигнатуры конструктора, неявно имеет тип any.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "\"[\"node_modules\", \"bower_components\", \"jspm_packages\"]\" и значение \"outDir\", если оно указано.", "one_of_Colon_6900": "один из:", "one_or_more_Colon_6901": "один или более:", "options_6024": "параметры", "or_JSX_element_expected_1145": "Ожидался элемент JSX или \"{\".", "or_expected_1144": "Ожидалось \"{\" или \";\".", "package_json_does_not_have_a_0_field_6100": "В package.json нет поля \"{0}\".", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "В файле \"package.json\" отсутствует запись \"typesVersions\", соответствующая версии \"{0}\".", "package_json_had_a_falsy_0_field_6220": "Файл \"package.json\" содержит поле \"{0}\" со значением false.", "package_json_has_0_field_1_that_references_2_6101": "package.json содержит поле \"{0}\" \"{1}\", которое ссылается на \"{2}\".", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "Файл \"package.json\" содержит запись \"typesVersions\" \"{0}\", которая не является допустимым диапазоном semver.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "Файл \"package.json\" содержит запись \"typesVersions\" \"{0}\", которая соответствует версии компилятора \"{1}\", выполняется поиск шаблона для сопоставления имени модуля \"{2}\".", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "Файл \"package.json\" содержит поле \"typesVersions\" с сопоставлениями путей, зависящих от версии.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "Область package.json \"{0}\" явно сопоставляет описатель \"{1}\" со значением NULL.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "Область package.json \"{0}\" имеет недопустимый тип для целевого объекта описателя \"{1}\"", "package_json_scope_0_has_no_imports_defined_6273": "Для области package.json \"{0}\" не определены операции импорта.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "Параметр paths указан, идет поиск шаблона, соответствующего имени модуля \"{0}\".", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "Модифик<PERSON><PERSON><PERSON><PERSON> readonly может отображаться только в объявлении свойства или сигнатуре индекса.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Модификатор типа \"readonly\" допускается только для типов литерала массива и кортежа.", "require_call_may_be_converted_to_an_import_80005": "Вызов \"require\" можно преобразовать в \"import\".", "resolution_mode_assertions_are_only_supported_when_moduleResolution_is_node16_or_nodenext_1452": "Утверждения \"режим разрешения\" поддерживаются только тогда, когда \"moduleResolution\" имеет значение \"node16\" или \"nodenext\".", "resolution_mode_assertions_are_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_wi_4125": "Утверждения ''режима разрешения'' нестабильны. Используйте ночной TypeScript, чтобы отключить эту ошибку. Попробуйте обновить с помощью ''npm install -D typescript@next''.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "\"resolution-mode\" можно задать лишь для импорта, распространяющегося только на тип.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "\"resolution-mode\" является единственным допустимым ключом для утверждений импорта типа.", "resolution_mode_should_be_either_require_or_import_1453": "\"resolution-mode\" должен иметь значение \"require\" или \"import\".", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "Параметр \"rootDirs\" задан; он используется для разрешения относительного имени модуля \"{0}\".", "super_can_only_be_referenced_in_a_derived_class_2335": "Ссылка на super может указываться только в производном классе.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "На super можно ссылаться только в элементах производных классов или литеральных выражениях объекта.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Не допускается ссылка на super в имени вычисляемого свойства.", "super_cannot_be_referenced_in_constructor_arguments_2336": "На super невозможно ссылаться в аргументах конструктора.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "super допускается только в элементах литеральных выражений объекта, если параметр target — ES2015 или выше.", "super_may_not_use_type_arguments_2754": "\"super\" не может использовать аргументы типа.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "Перед тем, как получить доступ к свойству \"super\" в конструкторе производного класса, необходимо вызвать \"super\".", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "super следует вызывать перед получением доступа к this в конструкторе производного класса.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "После super должен идти список аргументов или доступ к элементу.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "Доступ к свойству super разрешен только в конструкторе, функции-элементе или методе доступа к элементам производного класса.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "На this не может указывать ссылка в имени вычисляемого свойства.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "На this не могут указывать ссылки в теле модуля или пространства имен.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "На this не могут указывать ссылки в инициализаторе статического свойства.", "this_cannot_be_referenced_in_constructor_arguments_2333": "На this не могут указывать ссылки в аргументах конструктора.", "this_cannot_be_referenced_in_current_location_2332": "На this не могут указывать ссылки в текущем расположении.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "this неявно содержит тип any, так как он не имеет аннотации типа.", "true_for_ES2022_and_above_including_ESNext_6930": "true для ES2022 и выше, включая ESNext.", "true_if_composite_false_otherwise_6909": "\"true\", если \"composite\", \"false\" в противном случае", "tsc_Colon_The_TypeScript_Compiler_6922": "TSC: компилятор TypeScript", "type_Colon_6902": "тип:", "unique_symbol_types_are_not_allowed_here_1335": "Типы \"unique symbol\" здесь запрещены.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Типы \"unique symbol\" разрешены только у переменных в операторах с переменными.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Типы \"unique symbol\" невозможно использовать в объявлении переменной с именем привязки.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "Директиву \"use strict\" запрещено использовать со списком параметров, которые не являются простыми.", "use_strict_directive_used_here_1349": "Здесь используется директива \"use strict\".", "with_statements_are_not_allowed_in_an_async_function_block_1300": "Операторы with не разрешено использовать в блоке асинхронной функции.", "with_statements_are_not_allowed_in_strict_mode_1101": "Операторы with не разрешено использовать в строгом режиме.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Выражение \"yield\" неявно формирует результат типа \"any\", поскольку содержащий его генератор не имеет заметки типа возвращаемого значения.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Выражения yield не могут быть использованы в инициализаторе параметра."}