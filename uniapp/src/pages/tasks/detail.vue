<template>
  <view class="container">
    <!-- 任务信息 -->
    <view class="task-info" v-if="task">
      <view class="task-header">
        <text class="task-name">{{ task.name }}</text>
        <view class="task-status" :class="getStatusClass(task.status)">
          {{ getStatusText(task.status) }}
        </view>
      </view>
      <text class="task-time">创建时间: {{ formatTime(task.created_at) }}</text>
    </view>

    <!-- 功能模块 -->
    <view class="modules">
      <!-- 时间偏好设置 -->
      <view class="module-card">
        <view class="module-header">
          <text class="module-title">时间偏好设置</text>
          <button class="module-btn" @click="goToPreferences">设置</button>
        </view>
        <text class="module-desc">为教师和学生设置时间偏好</text>
      </view>

      <!-- 课程需求配置 -->
      <view class="module-card">
        <view class="module-header">
          <text class="module-title">课程需求配置</text>
          <button class="module-btn" @click="showGroupTeacherModal = true">配置</button>
        </view>
        <text class="module-desc">配置学生组与教师的课程需求</text>
        
        <!-- 已配置的课程需求列表 -->
        <view class="demands-list" v-if="groupTeachers.length > 0">
          <view 
            v-for="demand in groupTeachers" 
            :key="demand.id" 
            class="demand-item"
          >
            <text class="demand-text">
              {{ getGroupName(demand.group_id) }} - {{ getTeacherName(demand.teacher_id) }} 
              ({{ demand.subject }}, {{ demand.duration }}分钟)
            </text>
          </view>
        </view>
      </view>

      <!-- 执行排课 -->
      <view class="module-card">
        <view class="module-header">
          <text class="module-title">执行排课</text>
          <button 
            class="module-btn run-btn" 
            @click="runScheduling"
            :disabled="task?.status === 'running'"
          >
            {{ task?.status === 'running' ? '执行中...' : '开始排课' }}
          </button>
        </view>
        <text class="module-desc">基于配置的需求和偏好执行排课算法</text>
      </view>

      <!-- 查看结果 -->
      <view class="module-card">
        <view class="module-header">
          <text class="module-title">排课结果</text>
          <button class="module-btn" @click="goToSchedules">查看</button>
        </view>
        <text class="module-desc">查看排课结果和冲突分析</text>
      </view>
    </view>

    <!-- 课程需求配置弹窗 -->
    <view class="modal-overlay" v-if="showGroupTeacherModal" @click="closeGroupTeacherModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">配置课程需求</text>
          <text class="modal-close" @click="closeGroupTeacherModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">选择学生组</text>
            <picker 
              :range="groups" 
              range-key="name" 
              @change="onGroupChange"
              :value="groupTeacherForm.groupIndex"
            >
              <view class="picker-input">
                {{ groupTeacherForm.groupIndex >= 0 ? groups[groupTeacherForm.groupIndex].name : '请选择学生组' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">选择教师</text>
            <picker 
              :range="teachers" 
              range-key="name" 
              @change="onTeacherChange"
              :value="groupTeacherForm.teacherIndex"
            >
              <view class="picker-input">
                {{ groupTeacherForm.teacherIndex >= 0 ? teachers[groupTeacherForm.teacherIndex].name : '请选择教师' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">授课科目</text>
            <input 
              v-model="groupTeacherForm.subject" 
              class="form-input" 
              placeholder="请输入授课科目"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">课程时长(分钟)</text>
            <input 
              v-model.number="groupTeacherForm.duration" 
              class="form-input" 
              type="number"
              placeholder="请输入课程时长"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeGroupTeacherModal">取消</button>
          <button class="confirm-btn" @click="submitGroupTeacher" :disabled="!canSubmitGroupTeacher">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type ScheduleTask, type Group, type Teacher, type GroupTeacher } from '@/utils/api'

const taskId = ref<number>(0)
const task = ref<ScheduleTask | null>(null)
const groups = ref<Group[]>([])
const teachers = ref<Teacher[]>([])
const groupTeachers = ref<GroupTeacher[]>([])
const showGroupTeacherModal = ref(false)

const groupTeacherForm = ref({
  groupIndex: -1,
  teacherIndex: -1,
  subject: '',
  duration: 60
})

const canSubmitGroupTeacher = computed(() => {
  return groupTeacherForm.value.groupIndex >= 0 && 
         groupTeacherForm.value.teacherIndex >= 0 && 
         groupTeacherForm.value.subject.trim() && 
         groupTeacherForm.value.duration > 0
})

const loadTaskInfo = async () => {
  if (!taskId.value) return
  
  try {
    task.value = await api.tasks.getStatus(taskId.value)
  } catch (error) {
    console.error('加载任务信息失败:', error)
    utils.showError('加载任务信息失败')
  }
}

const loadData = async () => {
  try {
    utils.showLoading('加载数据...')
    const [groupsData, teachersData] = await Promise.all([
      api.groups.list(),
      api.teachers.list()
    ])
    
    groups.value = groupsData
    teachers.value = teachersData
  } catch (error) {
    console.error('加载数据失败:', error)
    utils.showError('加载数据失败')
  } finally {
    utils.hideLoading()
  }
}

const onGroupChange = (e: any) => {
  groupTeacherForm.value.groupIndex = e.detail.value
}

const onTeacherChange = (e: any) => {
  groupTeacherForm.value.teacherIndex = e.detail.value
}

const submitGroupTeacher = async () => {
  if (!canSubmitGroupTeacher.value || !taskId.value) return
  
  try {
    utils.showLoading('配置课程需求中...')
    
    const groupId = groups.value[groupTeacherForm.value.groupIndex].id
    const teacherId = teachers.value[groupTeacherForm.value.teacherIndex].id
    
    await api.groupTeacher.add(taskId.value, {
      group_id: groupId,
      teacher_id: teacherId,
      subject: groupTeacherForm.value.subject.trim(),
      duration: groupTeacherForm.value.duration
    })
    
    utils.showSuccess('配置课程需求成功')
    closeGroupTeacherModal()
    
    // 添加到本地列表
    groupTeachers.value.push({
      task_id: taskId.value,
      group_id: groupId,
      teacher_id: teacherId,
      subject: groupTeacherForm.value.subject.trim(),
      duration: groupTeacherForm.value.duration
    })
  } catch (error) {
    console.error('配置课程需求失败:', error)
    utils.showError('配置课程需求失败')
  } finally {
    utils.hideLoading()
  }
}

const closeGroupTeacherModal = () => {
  showGroupTeacherModal.value = false
  groupTeacherForm.value = {
    groupIndex: -1,
    teacherIndex: -1,
    subject: '',
    duration: 60
  }
}

const runScheduling = async () => {
  if (!taskId.value) return
  
  const confirmed = await utils.confirm('确定要开始排课吗？')
  if (!confirmed) return
  
  try {
    utils.showLoading('开始排课...')
    await api.tasks.run(taskId.value)
    utils.showSuccess('排课任务已启动')
    
    // 更新任务状态
    if (task.value) {
      task.value.status = 'running'
    }
  } catch (error) {
    console.error('启动排课失败:', error)
    utils.showError('启动排课失败')
  } finally {
    utils.hideLoading()
  }
}

const goToPreferences = () => {
  uni.navigateTo({
    url: `/pages/preferences/index?taskId=${taskId.value}`
  })
}

const goToSchedules = () => {
  uni.navigateTo({
    url: `/pages/schedules/index?taskId=${taskId.value}`
  })
}

const getGroupName = (groupId: number) => {
  const group = groups.value.find(g => g.id === groupId)
  return group ? group.name : `组#${groupId}`
}

const getTeacherName = (teacherId: number) => {
  const teacher = teachers.value.find(t => t.id === teacherId)
  return teacher ? teacher.name : `教师#${teacherId}`
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'status-pending'
    case 'running': return 'status-running'
    case 'completed': return 'status-completed'
    case 'failed': return 'status-failed'
    default: return 'status-pending'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待执行'
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'failed': return '执行失败'
    default: return '未知'
  }
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options

  if (options.id) {
    taskId.value = parseInt(options.id)
    loadTaskInfo()
    loadData()
  }
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.task-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.task-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.task-status {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-pending { background-color: #999; }
.status-running { background-color: #FF9500; }
.status-completed { background-color: #34C759; }
.status-failed { background-color: #FF3B30; }

.task-time {
  font-size: 24rpx;
  color: #666;
}

.modules {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.module-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.module-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.module-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.run-btn {
  background-color: #34C759;
}

.run-btn:disabled {
  background-color: #ccc;
}

.module-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.demands-list {
  margin-top: 20rpx;
}

.demand-item {
  background-color: #f9f9f9;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.demand-text {
  font-size: 26rpx;
  color: #333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 650rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.picker-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  background-color: white;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
