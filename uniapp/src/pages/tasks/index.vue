<template>
  <view class="container">
    <!-- 头部操作栏 -->
    <view class="header">
      <view class="header-title">排课任务</view>
      <button class="add-btn" @click="showAddModal = true">+ 创建任务</button>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view v-if="tasks.length === 0" class="empty-state">
        <text class="empty-text">暂无排课任务</text>
        <text class="empty-desc">点击右上角创建任务</text>
      </view>
      
      <view v-else>
        <view 
          v-for="task in tasks" 
          :key="task.id" 
          class="task-item"
          @click="goToTaskDetail(task.id)"
        >
          <view class="task-info">
            <view class="task-header">
              <view class="task-name">{{ task.name }}</view>
              <view class="task-status" :class="getStatusClass(task.status)">
                {{ getStatusText(task.status) }}
              </view>
            </view>
            <view class="task-meta">
              <text class="task-time">创建时间: {{ formatTime(task.created_at) }}</text>
              <text class="task-id">#{{ task.id }}</text>
            </view>
          </view>
          <view class="task-actions">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建任务弹窗 -->
    <view class="modal-overlay" v-if="showAddModal" @click="closeModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">创建排课任务</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>

        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">任务名称</text>
            <input
              v-model="form.name"
              class="form-input"
              placeholder="请输入任务名称"
              maxlength="50"
            />
          </view>
        </view>

        <view class="modal-footer">
          <button class="cancel-btn" @click="closeModal">取消</button>
          <button class="confirm-btn" @click="submitForm" :disabled="!canSubmit">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type ScheduleTask } from '@/utils/api'

const tasks = ref<ScheduleTask[]>([])
const showAddModal = ref(false)

const form = ref({
  name: ''
})

const canSubmit = computed(() => {
  return form.value.name.trim().length > 0
})

const loadTasks = async () => {
  try {
    utils.showLoading('加载任务列表...')
    // 注意：这里需要后端提供任务列表API，暂时使用空数组
    // tasks.value = await api.tasks.list()
    tasks.value = []
  } catch (error) {
    console.error('加载任务列表失败:', error)
    utils.showError('加载任务列表失败')
  } finally {
    utils.hideLoading()
  }
}

const submitForm = async () => {
  if (!canSubmit.value) return
  
  try {
    utils.showLoading('创建任务中...')
    const newTask = await api.tasks.create({
      name: form.value.name.trim()
    })
    
    utils.showSuccess('创建任务成功')
    closeModal()
    
    // 将新任务添加到列表中
    tasks.value.unshift(newTask)
  } catch (error) {
    console.error('创建任务失败:', error)
    utils.showError('创建任务失败')
  } finally {
    utils.hideLoading()
  }
}

const closeModal = () => {
  showAddModal.value = false
  form.value = {
    name: ''
  }
}

const goToTaskDetail = (taskId: number) => {
  uni.navigateTo({
    url: `/pages/tasks/detail?id=${taskId}`
  })
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'status-pending'
    case 'running':
      return 'status-running'
    case 'completed':
      return 'status-completed'
    case 'failed':
      return 'status-failed'
    default:
      return 'status-pending'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待执行'
    case 'running':
      return '执行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '执行失败'
    default:
      return '未知'
  }
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.task-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.task-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

.task-status {
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.status-pending {
  background-color: #999;
}

.status-running {
  background-color: #FF9500;
}

.status-completed {
  background-color: #34C759;
}

.status-failed {
  background-color: #FF3B30;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-time {
  font-size: 24rpx;
  color: #666;
}

.task-id {
  font-size: 24rpx;
  color: #999;
}

.task-actions {
  display: flex;
  align-items: center;
}

.arrow {
  font-size: 32rpx;
  color: #ccc;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
