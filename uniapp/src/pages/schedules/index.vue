<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="header-title">排课结果</text>
      <button class="refresh-btn" @click="loadSchedules">刷新</button>
    </view>

    <!-- 结果统计 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-number">{{ schedules.length }}</text>
        <text class="stat-label">已排课程</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ unscheduled.length }}</text>
        <text class="stat-label">冲突课程</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ totalCourses }}</text>
        <text class="stat-label">总课程数</text>
      </view>
    </view>

    <!-- 类型切换 -->
    <view class="type-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'scheduled' }"
        @click="currentTab = 'scheduled'"
      >
        已排课程 ({{ schedules.length }})
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'unscheduled' }"
        @click="currentTab = 'unscheduled'"
      >
        冲突课程 ({{ unscheduled.length }})
      </view>
    </view>

    <!-- 已排课程列表 -->
    <view v-if="currentTab === 'scheduled'" class="schedules-list">
      <view v-if="schedules.length === 0" class="empty-state">
        <text class="empty-text">暂无已排课程</text>
        <text class="empty-desc">请先执行排课任务</text>
      </view>
      
      <view v-else>
        <view 
          v-for="schedule in schedules" 
          :key="schedule.id" 
          class="schedule-item"
        >
          <view class="schedule-header">
            <text class="subject">{{ schedule.subject }}</text>
            <view class="weight-tag">权重: {{ schedule.weight }}</view>
          </view>
          <view class="schedule-info">
            <view class="info-row">
              <text class="label">教师:</text>
              <text class="value">{{ getTeacherName(schedule.teacher_id) }}</text>
            </view>
            <view class="info-row">
              <text class="label">学生组:</text>
              <text class="value">{{ getGroupName(schedule.group_id) }}</text>
            </view>
            <view class="info-row">
              <text class="label">时间:</text>
              <text class="value">{{ schedule.day }} {{ schedule.start_time }}-{{ schedule.end_time }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 冲突课程列表 -->
    <view v-if="currentTab === 'unscheduled'" class="unscheduled-list">
      <view v-if="unscheduled.length === 0" class="empty-state">
        <text class="empty-text">暂无冲突课程</text>
        <text class="empty-desc">所有课程都已成功排课</text>
      </view>
      
      <view v-else>
        <view 
          v-for="course in unscheduled" 
          :key="course.id" 
          class="unscheduled-item"
        >
          <view class="course-header">
            <text class="subject">{{ course.subject }}</text>
            <view class="conflict-tag">冲突</view>
          </view>
          <view class="course-info">
            <view class="info-row">
              <text class="label">教师:</text>
              <text class="value">{{ getTeacherName(course.teacher_id) }}</text>
            </view>
            <view class="info-row">
              <text class="label">学生组:</text>
              <text class="value">{{ getGroupName(course.group_id) }}</text>
            </view>
          </view>
          
          <!-- 冲突原因 -->
          <view class="conflict-reasons" v-if="course.reasons && course.reasons.length > 0">
            <text class="reasons-title">冲突原因:</text>
            <view class="reasons-list">
              <text 
                v-for="(reason, index) in course.reasons" 
                :key="index" 
                class="reason-item"
              >
                • {{ reason }}
              </text>
            </view>
          </view>
          
          <!-- 解决建议 -->
          <view class="solutions" v-if="course.solutions && course.solutions.length > 0">
            <text class="solutions-title">解决建议:</text>
            <view class="solutions-list">
              <text 
                v-for="(solution, index) in course.solutions" 
                :key="index" 
                class="solution-item"
              >
                • {{ solution }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type Schedule, type UnscheduledCourse, type Teacher, type Group } from '@/utils/api'

const taskId = ref<number>(0)
const currentTab = ref<'scheduled' | 'unscheduled'>('scheduled')
const schedules = ref<Schedule[]>([])
const unscheduled = ref<UnscheduledCourse[]>([])
const teachers = ref<Teacher[]>([])
const groups = ref<Group[]>([])

const totalCourses = computed(() => {
  return schedules.value.length + unscheduled.value.length
})

const loadSchedules = async () => {
  if (!taskId.value) return
  
  try {
    utils.showLoading('加载排课结果...')
    
    const [schedulesData, unscheduledData, teachersData, groupsData] = await Promise.all([
      api.tasks.getSchedules(taskId.value),
      api.tasks.getUnscheduled(taskId.value),
      api.teachers.list(),
      api.groups.list()
    ])
    
    schedules.value = schedulesData
    unscheduled.value = unscheduledData
    teachers.value = teachersData
    groups.value = groupsData
  } catch (error) {
    console.error('加载排课结果失败:', error)
    utils.showError('加载排课结果失败')
  } finally {
    utils.hideLoading()
  }
}

const getTeacherName = (teacherId: number) => {
  const teacher = teachers.value.find(t => t.id === teacherId)
  return teacher ? teacher.name : `教师#${teacherId}`
}

const getGroupName = (groupId: number) => {
  const group = groups.value.find(g => g.id === groupId)
  return group ? group.name : `组#${groupId}`
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options
  
  if (options.taskId) {
    taskId.value = parseInt(options.taskId)
    loadSchedules()
  }
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.refresh-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  display: block;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.type-tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: white;
}

.tab-item.active {
  color: #007AFF;
  background-color: #f0f8ff;
  font-weight: bold;
}

.schedules-list, .unscheduled-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.schedule-item, .unscheduled-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-item:last-child, .unscheduled-item:last-child {
  border-bottom: none;
}

.schedule-header, .course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.subject {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.weight-tag {
  background-color: #34C759;
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.conflict-tag {
  background-color: #FF3B30;
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.schedule-info, .course-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.conflict-reasons, .solutions {
  margin-top: 20rpx;
}

.reasons-title, .solutions-title {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.reasons-list, .solutions-list {
  padding-left: 20rpx;
}

.reason-item, .solution-item {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
  line-height: 1.5;
}

.reason-item {
  color: #FF3B30;
}

.solution-item {
  color: #007AFF;
}
</style>
