<template>
  <view class="container">
    <!-- 头部操作栏 -->
    <view class="header">
      <view class="header-title">学生管理</view>
      <button class="add-btn" @click="showAddModal = true">+ 添加学生</button>
    </view>

    <!-- 学生列表 -->
    <view class="student-list">
      <view v-if="students.length === 0" class="empty-state">
        <text class="empty-text">暂无学生数据</text>
        <text class="empty-desc">点击右上角添加学生</text>
      </view>
      
      <view v-else>
        <view 
          v-for="student in students" 
          :key="student.id" 
          class="student-item"
        >
          <view class="student-info">
            <view class="student-name">{{ student.name }}</view>
          </view>
          <view class="student-actions">
            <text class="student-id">#{{ student.id }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加学生弹窗 -->
    <view class="modal-overlay" v-if="showAddModal" @click="closeModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">添加学生</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>

        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">学生姓名</text>
            <input
              v-model="form.name"
              class="form-input"
              placeholder="请输入学生姓名"
              maxlength="20"
              :focus="false"
              @input="onNameInput"
            />
          </view>
        </view>

        <view class="modal-footer">
          <view class="cancel-btn" @tap="closeModal">取消</view>
          <view class="confirm-btn" :class="{ disabled: !canSubmit }" @tap="submitForm">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type Student } from '@/utils/api'

const students = ref<Student[]>([])
const showAddModal = ref(false)

const form = ref({
  name: ''
})

const canSubmit = computed(() => {
  return form.value.name.trim().length > 0
})

const loadStudents = async () => {
  try {
    utils.showLoading('加载学生列表...')
    students.value = await api.students.list()
  } catch (error) {
    console.error('加载学生列表失败:', error)
    utils.showError('加载学生列表失败')
  } finally {
    utils.hideLoading()
  }
}

const onNameInput = (e: any) => {
  form.value.name = e.detail.value
}

const submitForm = async () => {
  if (!canSubmit.value) {
    utils.showError('请填写学生姓名')
    return
  }

  try {
    utils.showLoading('添加学生中...')
    await api.students.create({
      name: form.value.name.trim()
    })

    utils.showSuccess('添加学生成功')
    closeModal()
    loadStudents()
  } catch (error) {
    console.error('添加学生失败:', error)
    utils.showError('添加学生失败')
  } finally {
    utils.hideLoading()
  }
}

const closeModal = () => {
  showAddModal.value = false
  form.value = {
    name: ''
  }
}

onMounted(() => {
  loadStudents()
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.student-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.student-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.student-item:last-child {
  border-bottom: none;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.student-actions {
  display: flex;
  align-items: center;
}

.student-id {
  font-size: 24rpx;
  color: #999;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  height: auto !important;
  min-height: 80rpx;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
  text-align: center;
  cursor: pointer;
  user-select: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn.disabled {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}
</style>
