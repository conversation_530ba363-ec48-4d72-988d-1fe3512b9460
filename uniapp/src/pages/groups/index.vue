<template>
  <view class="container">
    <!-- 头部操作栏 -->
    <view class="header">
      <view class="header-title">学生组管理</view>
      <button class="add-btn" @click="showAddModal = true">+ 创建学生组</button>
    </view>

    <!-- 学生组列表 -->
    <view class="group-list">
      <view v-if="groups.length === 0" class="empty-state">
        <text class="empty-text">暂无学生组数据</text>
        <text class="empty-desc">点击右上角创建学生组</text>
      </view>
      
      <view v-else>
        <view 
          v-for="group in groups" 
          :key="group.id" 
          class="group-item"
        >
          <view class="group-info">
            <view class="group-header">
              <view class="group-name">{{ group.name }}</view>
              <view class="group-subject">{{ group.subject }}</view>
            </view>
            <view class="group-students" v-if="group.students && group.students.length > 0">
              <text class="student-count">学生: {{ group.students.length }}人</text>
              <view class="student-names">
                <text 
                  v-for="student in group.students" 
                  :key="student.id" 
                  class="student-name"
                >
                  {{ student.name }}
                </text>
              </view>
            </view>
          </view>
          <view class="group-actions">
            <text class="group-id">#{{ group.id }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建学生组弹窗 -->
    <view class="modal-overlay" v-if="showAddModal" @click="closeModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">创建学生组</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">组名称</text>
            <input
              v-model="form.name"
              class="form-input"
              placeholder="请输入组名称"
              maxlength="20"
              :focus="false"
              @input="onNameInput"
            />
          </view>

          <view class="form-item">
            <text class="form-label">授课科目</text>
            <input
              v-model="form.subject"
              class="form-input"
              placeholder="请输入授课科目"
              maxlength="20"
              :focus="false"
              @input="onSubjectInput"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">选择学生</text>
            <view class="students-selector">
              <view v-if="availableStudents.length === 0" class="no-students">
                <text>暂无可选学生，请先添加学生</text>
              </view>
              <view v-else class="students-list">
                <view 
                  v-for="student in availableStudents" 
                  :key="student.id" 
                  class="student-option"
                  :class="{ selected: form.studentIds.includes(student.id) }"
                  @click="toggleStudent(student.id)"
                >
                  <text class="student-option-name">{{ student.name }}</text>
                  <text class="student-option-check" v-if="form.studentIds.includes(student.id)">✓</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="cancel-btn" @tap="closeModal">取消</view>
          <view class="confirm-btn" :class="{ disabled: !canSubmit }" @tap="submitForm">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type Group, type Student } from '@/utils/api'

const groups = ref<Group[]>([])
const availableStudents = ref<Student[]>([])
const showAddModal = ref(false)

const form = ref({
  name: '',
  subject: '',
  studentIds: [] as number[]
})

const canSubmit = computed(() => {
  return form.value.name.trim() && form.value.subject.trim() && form.value.studentIds.length > 0
})

const loadGroups = async () => {
  try {
    utils.showLoading('加载学生组列表...')
    groups.value = await api.groups.list()
  } catch (error) {
    console.error('加载学生组列表失败:', error)
    utils.showError('加载学生组列表失败')
  } finally {
    utils.hideLoading()
  }
}

const loadStudents = async () => {
  try {
    availableStudents.value = await api.students.list()
  } catch (error) {
    console.error('加载学生列表失败:', error)
    utils.showError('加载学生列表失败')
  }
}

const onNameInput = (e: any) => {
  form.value.name = e.detail.value
}

const onSubjectInput = (e: any) => {
  form.value.subject = e.detail.value
}

const toggleStudent = (studentId: number) => {
  const index = form.value.studentIds.indexOf(studentId)
  if (index > -1) {
    form.value.studentIds.splice(index, 1)
  } else {
    form.value.studentIds.push(studentId)
  }
}

const submitForm = async () => {
  if (!canSubmit.value) {
    utils.showError('请填写完整信息并选择学生')
    return
  }

  try {
    utils.showLoading('创建学生组中...')
    await api.groups.create({
      name: form.value.name.trim(),
      subject: form.value.subject.trim(),
      student_ids: form.value.studentIds
    })

    utils.showSuccess('创建学生组成功')
    closeModal()
    loadGroups()
  } catch (error) {
    console.error('创建学生组失败:', error)
    utils.showError('创建学生组失败')
  } finally {
    utils.hideLoading()
  }
}

const closeModal = () => {
  showAddModal.value = false
  form.value = {
    name: '',
    subject: '',
    studentIds: []
  }
}

onMounted(() => {
  loadGroups()
  loadStudents()
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.group-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.group-item:last-child {
  border-bottom: none;
}

.group-info {
  flex: 1;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.group-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

.group-subject {
  background-color: #007AFF;
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.group-students {
  margin-top: 10rpx;
}

.student-count {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.student-names {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.student-name {
  background-color: #f0f0f0;
  color: #333;
  padding: 5rpx 12rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
}

.group-actions {
  display: flex;
  align-items: center;
}

.group-id {
  font-size: 24rpx;
  color: #999;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 650rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  height: auto !important;
  min-height: 80rpx;
  line-height: 1.4;
}

.students-selector {
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.no-students {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

.students-list {
  padding: 10rpx;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  background-color: #f9f9f9;
}

.student-option.selected {
  background-color: #007AFF;
  color: white;
}

.student-option-name {
  font-size: 28rpx;
}

.student-option-check {
  font-size: 24rpx;
  font-weight: bold;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
  text-align: center;
  cursor: pointer;
  user-select: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn.disabled {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}
</style>
