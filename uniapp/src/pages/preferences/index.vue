<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="header-title">时间偏好设置</text>
      <button class="add-btn" @click="showAddModal = true">+ 添加偏好</button>
    </view>

    <!-- 类型切换 -->
    <view class="type-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentType === 'teacher' }"
        @click="currentType = 'teacher'"
      >
        教师偏好
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentType === 'student' }"
        @click="currentType = 'student'"
      >
        学生偏好
      </view>
    </view>

    <!-- 偏好列表 -->
    <view class="preferences-list">
      <view v-if="filteredPreferences.length === 0" class="empty-state">
        <text class="empty-text">暂无{{ currentType === 'teacher' ? '教师' : '学生' }}时间偏好</text>
        <text class="empty-desc">点击右上角添加偏好</text>
      </view>
      
      <view v-else>
        <view 
          v-for="preference in filteredPreferences" 
          :key="preference.id" 
          class="preference-item"
        >
          <view class="preference-info">
            <view class="preference-header">
              <text class="entity-name">{{ getEntityName(preference.entity_type, preference.entity_id) }}</text>
              <view class="priority-tag" :class="getPriorityClass(preference.priority)">
                {{ getPriorityText(preference.priority) }}
              </view>
            </view>
            <view class="time-info">
              <text class="day">{{ preference.day }}</text>
              <text class="time">{{ preference.start_time }} - {{ preference.end_time }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加偏好弹窗 -->
    <view class="modal-overlay" v-if="showAddModal" @click="closeModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">添加时间偏好</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">类型</text>
            <picker 
              :range="['教师', '学生']" 
              @change="onTypeChange"
              :value="form.typeIndex"
            >
              <view class="picker-input">
                {{ form.typeIndex >= 0 ? ['教师', '学生'][form.typeIndex] : '请选择类型' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item" v-if="form.typeIndex >= 0">
            <text class="form-label">选择{{ form.typeIndex === 0 ? '教师' : '学生' }}</text>
            <picker 
              :range="form.typeIndex === 0 ? teachers : students" 
              range-key="name" 
              @change="onEntityChange"
              :value="form.entityIndex"
            >
              <view class="picker-input">
                {{ getSelectedEntityName() }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">星期</text>
            <picker 
              :range="weekDays" 
              @change="onDayChange"
              :value="form.dayIndex"
            >
              <view class="picker-input">
                {{ form.dayIndex >= 0 ? weekDays[form.dayIndex] : '请选择星期' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">开始时间</text>
            <picker 
              mode="time" 
              @change="onStartTimeChange"
              :value="form.startTime"
            >
              <view class="picker-input">
                {{ form.startTime || '请选择开始时间' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">结束时间</text>
            <picker 
              mode="time" 
              @change="onEndTimeChange"
              :value="form.endTime"
            >
              <view class="picker-input">
                {{ form.endTime || '请选择结束时间' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">优先级</text>
            <picker 
              :range="priorities" 
              range-key="text"
              @change="onPriorityChange"
              :value="form.priorityIndex"
            >
              <view class="picker-input">
                {{ form.priorityIndex >= 0 ? priorities[form.priorityIndex].text : '请选择优先级' }}
              </view>
            </picker>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeModal">取消</button>
          <button class="confirm-btn" @click="submitForm" :disabled="!canSubmit">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type TimePreference, type Teacher, type Student } from '@/utils/api'

const taskId = ref<number>(0)
const currentType = ref<'teacher' | 'student'>('teacher')
const preferences = ref<TimePreference[]>([])
const teachers = ref<Teacher[]>([])
const students = ref<Student[]>([])
const showAddModal = ref(false)

const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const priorities = [
  { value: 1, text: '首选时间' },
  { value: 2, text: '次选时间' },
  { value: 3, text: '普通空闲' }
]

const form = ref({
  typeIndex: -1,
  entityIndex: -1,
  dayIndex: -1,
  startTime: '',
  endTime: '',
  priorityIndex: -1
})

const filteredPreferences = computed(() => {
  return preferences.value.filter(p => p.entity_type === currentType.value)
})

const canSubmit = computed(() => {
  return form.value.typeIndex >= 0 && 
         form.value.entityIndex >= 0 && 
         form.value.dayIndex >= 0 && 
         form.value.startTime && 
         form.value.endTime && 
         form.value.priorityIndex >= 0
})

const loadData = async () => {
  try {
    utils.showLoading('加载数据...')
    const [teachersData, studentsData] = await Promise.all([
      api.teachers.list(),
      api.students.list()
    ])
    
    teachers.value = teachersData
    students.value = studentsData
  } catch (error) {
    console.error('加载数据失败:', error)
    utils.showError('加载数据失败')
  } finally {
    utils.hideLoading()
  }
}

const onTypeChange = (e: any) => {
  form.value.typeIndex = e.detail.value
  form.value.entityIndex = -1 // 重置实体选择
}

const onEntityChange = (e: any) => {
  form.value.entityIndex = e.detail.value
}

const onDayChange = (e: any) => {
  form.value.dayIndex = e.detail.value
}

const onStartTimeChange = (e: any) => {
  form.value.startTime = e.detail.value
}

const onEndTimeChange = (e: any) => {
  form.value.endTime = e.detail.value
}

const onPriorityChange = (e: any) => {
  form.value.priorityIndex = e.detail.value
}

const getSelectedEntityName = () => {
  if (form.value.typeIndex < 0 || form.value.entityIndex < 0) {
    return form.value.typeIndex === 0 ? '请选择教师' : '请选择学生'
  }
  
  const entities = form.value.typeIndex === 0 ? teachers.value : students.value
  return entities[form.value.entityIndex]?.name || '未知'
}

const submitForm = async () => {
  if (!canSubmit.value || !taskId.value) return
  
  try {
    utils.showLoading('添加时间偏好中...')
    
    const entityType = form.value.typeIndex === 0 ? 'teacher' : 'student'
    const entities = form.value.typeIndex === 0 ? teachers.value : students.value
    const entityId = entities[form.value.entityIndex].id
    
    const preferenceData = {
      day: weekDays[form.value.dayIndex],
      start_time: form.value.startTime,
      end_time: form.value.endTime,
      priority: priorities[form.value.priorityIndex].value
    }
    
    if (entityType === 'teacher') {
      await api.preferences.addTeacher(taskId.value, entityId, preferenceData)
    } else {
      await api.preferences.addStudent(taskId.value, entityId, preferenceData)
    }
    
    utils.showSuccess('添加时间偏好成功')
    closeModal()
    
    // 添加到本地列表
    preferences.value.push({
      task_id: taskId.value,
      entity_type: entityType,
      entity_id: entityId,
      ...preferenceData
    })
  } catch (error) {
    console.error('添加时间偏好失败:', error)
    utils.showError('添加时间偏好失败')
  } finally {
    utils.hideLoading()
  }
}

const closeModal = () => {
  showAddModal.value = false
  form.value = {
    typeIndex: -1,
    entityIndex: -1,
    dayIndex: -1,
    startTime: '',
    endTime: '',
    priorityIndex: -1
  }
}

const getEntityName = (entityType: string, entityId: number) => {
  const entities = entityType === 'teacher' ? teachers.value : students.value
  const entity = entities.find(e => e.id === entityId)
  return entity ? entity.name : `${entityType}#${entityId}`
}

const getPriorityClass = (priority: number) => {
  switch (priority) {
    case 1: return 'priority-high'
    case 2: return 'priority-medium'
    case 3: return 'priority-low'
    default: return 'priority-low'
  }
}

const getPriorityText = (priority: number) => {
  const p = priorities.find(p => p.value === priority)
  return p ? p.text : '未知'
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options

  if (options.taskId) {
    taskId.value = parseInt(options.taskId)
  }

  loadData()
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.type-tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: white;
}

.tab-item.active {
  color: #007AFF;
  background-color: #f0f8ff;
  font-weight: bold;
}

.preferences-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.preference-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.entity-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.priority-tag {
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.priority-high {
  background-color: #FF3B30;
}

.priority-medium {
  background-color: #FF9500;
}

.priority-low {
  background-color: #34C759;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.day {
  font-size: 26rpx;
  color: #007AFF;
  background-color: #f0f8ff;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
}

.time {
  font-size: 26rpx;
  color: #666;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 650rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.picker-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  background-color: white;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
