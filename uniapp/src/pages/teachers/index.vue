<template>
  <view class="container">
    <!-- 头部操作栏 -->
    <view class="header">
      <view class="header-title">教师管理</view>
      <button class="add-btn" @click="showAddModal = true">+ 添加教师</button>
    </view>

    <!-- 教师列表 -->
    <view class="teacher-list">
      <view v-if="teachers.length === 0" class="empty-state">
        <text class="empty-text">暂无教师数据</text>
        <text class="empty-desc">点击右上角添加教师</text>
      </view>
      
      <view v-else>
        <view 
          v-for="teacher in teachers" 
          :key="teacher.id" 
          class="teacher-item"
        >
          <view class="teacher-info">
            <view class="teacher-name">{{ teacher.name }}</view>
            <view class="teacher-subjects">
              <text 
                v-for="subject in teacher.subjects" 
                :key="subject" 
                class="subject-tag"
              >
                {{ subject }}
              </text>
            </view>
          </view>
          <view class="teacher-actions">
            <text class="teacher-id">#{{ teacher.id }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加教师弹窗 -->
    <uni-popup ref="popup" type="center" :mask-click="false">
      <view class="modal" v-if="showAddModal">
        <view class="modal-header">
          <text class="modal-title">添加教师</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">教师姓名</text>
            <input 
              v-model="form.name" 
              class="form-input" 
              placeholder="请输入教师姓名"
              maxlength="20"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">授课科目</text>
            <view class="subjects-input">
              <input 
                v-model="subjectInput" 
                class="form-input" 
                placeholder="请输入科目名称"
                @confirm="addSubject"
              />
              <button class="add-subject-btn" @click="addSubject">添加</button>
            </view>
            
            <view class="subjects-list" v-if="form.subjects.length > 0">
              <view 
                v-for="(subject, index) in form.subjects" 
                :key="index" 
                class="subject-item"
              >
                <text class="subject-name">{{ subject }}</text>
                <text class="remove-subject" @click="removeSubject(index)">×</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeModal">取消</button>
          <button class="confirm-btn" @click="submitForm" :disabled="!canSubmit">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api, utils, type Teacher } from '@/utils/api'

const teachers = ref<Teacher[]>([])
const showAddModal = ref(false)
const subjectInput = ref('')

const form = ref({
  name: '',
  subjects: [] as string[]
})

const canSubmit = computed(() => {
  return form.value.name.trim() && form.value.subjects.length > 0
})

const loadTeachers = async () => {
  try {
    utils.showLoading('加载教师列表...')
    teachers.value = await api.teachers.list()
  } catch (error) {
    console.error('加载教师列表失败:', error)
    utils.showError('加载教师列表失败')
  } finally {
    utils.hideLoading()
  }
}

const addSubject = () => {
  const subject = subjectInput.value.trim()
  if (!subject) {
    utils.showError('请输入科目名称')
    return
  }
  
  if (form.value.subjects.includes(subject)) {
    utils.showError('科目已存在')
    return
  }
  
  form.value.subjects.push(subject)
  subjectInput.value = ''
}

const removeSubject = (index: number) => {
  form.value.subjects.splice(index, 1)
}

const submitForm = async () => {
  if (!canSubmit.value) return
  
  try {
    utils.showLoading('添加教师中...')
    await api.teachers.create({
      name: form.value.name.trim(),
      subjects: form.value.subjects
    })
    
    utils.showSuccess('添加教师成功')
    closeModal()
    loadTeachers()
  } catch (error) {
    console.error('添加教师失败:', error)
    utils.showError('添加教师失败')
  } finally {
    utils.hideLoading()
  }
}

const closeModal = () => {
  showAddModal.value = false
  form.value = {
    name: '',
    subjects: []
  }
  subjectInput.value = ''
}

onMounted(() => {
  loadTeachers()
})
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.teacher-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

.teacher-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.teacher-item:last-child {
  border-bottom: none;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.teacher-subjects {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.subject-tag {
  background-color: #007AFF;
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.teacher-actions {
  display: flex;
  align-items: center;
}

.teacher-id {
  font-size: 24rpx;
  color: #999;
}

.modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.subjects-input {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.subjects-input .form-input {
  flex: 1;
}

.add-subject-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  font-size: 24rpx;
}

.subjects-list {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.subject-item {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
}

.subject-name {
  font-size: 24rpx;
  color: #333;
  margin-right: 10rpx;
}

.remove-subject {
  font-size: 30rpx;
  color: #999;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
