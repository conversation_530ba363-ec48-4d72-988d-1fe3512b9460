<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
onLaunch(() => {
  console.log("App Launch");
});
onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>
<style>
/* 全局样式 - 修复 UniApp 默认样式问题 */

/* 修复输入框默认高度问题 */
uni-input {
  height: auto !important;
}

/* 修复按钮默认样式 */
uni-button {
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: inherit !important;
}

/* 修复 view 元素的默认样式 */
uni-view {
  box-sizing: border-box;
}

/* 修复文本元素的默认样式 */
uni-text {
  line-height: inherit;
}
</style>
